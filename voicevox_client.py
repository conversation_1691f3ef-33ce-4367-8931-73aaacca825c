import requests
import json
import pygame
import io
import time
import threading
from typing import Optional, Dict, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class VoiceVoxClient:
    def __init__(self, voicevox_url: str = "http://localhost:50021", config_file: str = "config.json"):
        """
        Initialize VOICEVOX client with optimized settings

        Args:
            voicevox_url: URL of the VOICEVOX engine (default: localhost:50021)
            config_file: Path to configuration file
        """
        # Load configuration
        self.config = self._load_config(config_file)
        self.base_url = self.config.get("voicevox", {}).get("engine_url", voicevox_url)
        self.speaker_id = self.config.get("voicevox", {}).get("default_speaker_id", 1)

        # Performance settings
        perf_config = self.config.get("voicevox", {}).get("performance", {})
        self.enable_streaming = perf_config.get("enable_streaming", True)
        self.cache_audio_queries = perf_config.get("cache_audio_queries", True)
        self.max_cache_size = perf_config.get("max_cache_size", 100)

        # Audio settings
        audio_config = self.config.get("voicevox", {}).get("audio", {})
        sample_rate = audio_config.get("sample_rate", 24000)
        buffer_size = audio_config.get("buffer_size", 1024)

        # Initialize pygame mixer with optimized settings
        pygame.mixer.pre_init(frequency=sample_rate, size=-16, channels=1, buffer=buffer_size)
        pygame.mixer.init()

        # Setup optimized HTTP session
        self.session = self._setup_session()

        # Cache for audio queries
        self.audio_query_cache: Dict[str, Any] = {}

        # Preload speakers if enabled
        if perf_config.get("preload_speakers", True):
            self._preload_speakers()

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Config file {config_file} not found, using defaults")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing config file: {e}, using defaults")
            return {}

    def _setup_session(self) -> requests.Session:
        """Setup optimized HTTP session with connection pooling and retries"""
        session = requests.Session()

        # Get optimization settings
        opt_config = self.config.get("voicevox", {}).get("optimization", {})

        # Setup retry strategy
        retry_strategy = Retry(
            total=opt_config.get("retry_attempts", 2),
            backoff_factor=opt_config.get("retry_delay", 0.1),
            status_forcelist=[429, 500, 502, 503, 504],
        )

        # Setup HTTP adapter with connection pooling
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=opt_config.get("connection_pool_size", 5),
            pool_maxsize=opt_config.get("connection_pool_size", 5)
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Keep-alive headers
        if opt_config.get("keep_alive_connections", True):
            session.headers.update({'Connection': 'keep-alive'})

        return session

    def _preload_speakers(self):
        """Preload speaker information for faster access"""
        try:
            self.speakers_cache = self.get_speakers()
            if self.speakers_cache:
                print(f"✓ Preloaded {len(self.speakers_cache)} speakers")
        except Exception as e:
            print(f"Warning: Could not preload speakers: {e}")
            self.speakers_cache = None

    def check_voicevox_status(self) -> bool:
        """Check if VOICEVOX engine is running with optimized timeout"""
        try:
            timeout = self.config.get("voicevox", {}).get("timeout_settings", {}).get("connection_timeout", 3)
            response = self.session.get(f"{self.base_url}/version", timeout=timeout)
            if response.status_code == 200:
                version_info = response.json()
                print(f"VOICEVOX Engine connected - Version: {version_info}")
                return True
            return False
        except Exception as e:
            print(f"VOICEVOX Engine not available: {e}")
            return False
    
    def get_speakers(self) -> Optional[list]:
        """Get available speakers from VOICEVOX with caching"""
        # Return cached speakers if available
        if hasattr(self, 'speakers_cache') and self.speakers_cache:
            return self.speakers_cache

        try:
            timeout = self.config.get("voicevox", {}).get("timeout_settings", {}).get("connection_timeout", 3)
            response = self.session.get(f"{self.base_url}/speakers", timeout=timeout)
            if response.status_code == 200:
                speakers = response.json()
                # Cache the speakers
                self.speakers_cache = speakers
                return speakers
            return None
        except Exception as e:
            print(f"Error getting speakers: {e}")
            return None
    
    def text_to_speech(self, text: str, speaker_id: Optional[int] = None) -> bool:
        """
        Convert text to speech and play it with optimized latency

        Args:
            text: Text to convert to speech
            speaker_id: Speaker ID to use (default: self.speaker_id)

        Returns:
            True if successful, False otherwise
        """
        if speaker_id is None:
            speaker_id = self.speaker_id

        start_time = time.time()

        try:
            # Get timeout configuration first
            timeout_config = self.config.get("voicevox", {}).get("timeout_settings", {})

            # Check cache first
            cache_key = f"{text}_{speaker_id}"
            audio_query = None

            if self.cache_audio_queries and cache_key in self.audio_query_cache:
                audio_query = self.audio_query_cache[cache_key]
                if self.config.get("voicevox", {}).get("debug", {}).get("measure_latency", True):
                    print(f"🚀 Using cached audio query")
            else:
                # Step 1: Create audio query with optimized timeout
                query_timeout = timeout_config.get("audio_query_timeout", 5)

                query_response = self.session.post(
                    f"{self.base_url}/audio_query",
                    params={"text": text, "speaker": speaker_id},
                    timeout=query_timeout
                )

                if query_response.status_code != 200:
                    print(f"Error creating audio query: {query_response.status_code}")
                    return False

                audio_query = query_response.json()

                # Cache the audio query if enabled
                if self.cache_audio_queries:
                    if len(self.audio_query_cache) >= self.max_cache_size:
                        # Remove oldest entry
                        oldest_key = next(iter(self.audio_query_cache))
                        del self.audio_query_cache[oldest_key]
                    self.audio_query_cache[cache_key] = audio_query

            # Apply audio settings from config
            audio_config = self.config.get("voicevox", {}).get("audio", {})
            if audio_config.get("speed_scale") != 1.0:
                audio_query["speedScale"] = audio_config.get("speed_scale", 1.0)
            if audio_config.get("pitch_scale") != 0.0:
                audio_query["pitchScale"] = audio_config.get("pitch_scale", 0.0)
            if audio_config.get("intonation_scale") != 1.0:
                audio_query["intonationScale"] = audio_config.get("intonation_scale", 1.0)

            # Step 2: Generate audio with optimized timeout
            synthesis_timeout = timeout_config.get("synthesis_timeout", 10)
            synthesis_response = self.session.post(
                f"{self.base_url}/synthesis",
                params={"speaker": speaker_id},
                json=audio_query,
                timeout=synthesis_timeout
            )

            if synthesis_response.status_code != 200:
                print(f"Error generating audio: {synthesis_response.status_code}")
                return False

            # Step 3: Play audio with optimized buffer
            audio_data = synthesis_response.content
            audio_file = io.BytesIO(audio_data)

            pygame.mixer.music.load(audio_file)

            # Apply volume setting
            volume = audio_config.get("volume", 1.0)
            pygame.mixer.music.set_volume(volume)

            pygame.mixer.music.play()

            # Optimized waiting - check less frequently for better performance
            while pygame.mixer.music.get_busy():
                pygame.time.wait(50)  # Reduced from 100ms to 50ms

            # Log latency if enabled
            if self.config.get("voicevox", {}).get("debug", {}).get("measure_latency", True):
                total_time = time.time() - start_time
                print(f"⏱️ TTS latency: {total_time:.2f}s")

            return True

        except Exception as e:
            print(f"Error in text-to-speech: {e}")
            return False

    def set_speaker(self, speaker_id: int):
        """Set the default speaker ID"""
        self.speaker_id = speaker_id
        print(f"Speaker set to ID: {speaker_id}")

    def preload_audio_query(self, text: str, speaker_id: Optional[int] = None):
        """Preload audio query for faster synthesis later"""
        if not self.cache_audio_queries:
            return

        if speaker_id is None:
            speaker_id = self.speaker_id

        cache_key = f"{text}_{speaker_id}"
        if cache_key in self.audio_query_cache:
            return  # Already cached

        try:
            timeout_config = self.config.get("voicevox", {}).get("timeout_settings", {})
            query_timeout = timeout_config.get("audio_query_timeout", 5)

            query_response = self.session.post(
                f"{self.base_url}/audio_query",
                params={"text": text, "speaker": speaker_id},
                timeout=query_timeout
            )

            if query_response.status_code == 200:
                audio_query = query_response.json()

                # Manage cache size
                if len(self.audio_query_cache) >= self.max_cache_size:
                    oldest_key = next(iter(self.audio_query_cache))
                    del self.audio_query_cache[oldest_key]

                self.audio_query_cache[cache_key] = audio_query
                print(f"✓ Preloaded audio query for: {text[:30]}...")

        except Exception as e:
            print(f"Warning: Could not preload audio query: {e}")

    def clear_cache(self):
        """Clear the audio query cache"""
        self.audio_query_cache.clear()
        print("✓ Audio query cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cache_size": len(self.audio_query_cache),
            "max_cache_size": self.max_cache_size,
            "cache_enabled": self.cache_audio_queries,
            "cached_queries": list(self.audio_query_cache.keys())
        }

    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration at runtime"""
        self.config.update(new_config)

        # Update relevant settings
        perf_config = self.config.get("voicevox", {}).get("performance", {})
        self.cache_audio_queries = perf_config.get("cache_audio_queries", True)
        self.max_cache_size = perf_config.get("max_cache_size", 100)

        print("✓ Configuration updated")

    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'session'):
            self.session.close()
