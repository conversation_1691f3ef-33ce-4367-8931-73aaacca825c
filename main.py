import speech_recognition as sr
import requests
import time
import json
import random
from typing import Optional, List, Dict, Any
from voicevox_client import VoiceVoxClient
from subtitle_manager import SubtitleManager
from vbaudio_manager import VBAudioManager
from vroid_manager import VRoidManager
from translation_manager import TranslationManager

class AIVtuber:
    def __init__(self):
        # Load configuration
        self.config = self._load_config("config.json")

        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.is_listening = False

        # Conversation history
        self.conversation_history: List[Dict[str, str]] = []
        self.max_history_length = 10  # Keep last 10 exchanges

        # Initialize components
        self.voicevox = VoiceVoxClient(config_file="config.json")
        self.use_voice_output = self.voicevox.check_voicevox_status()

        # Initialize subtitle manager
        self.subtitle_manager = SubtitleManager(self.config)

        # Initialize VBAudio manager
        self.vbaudio_manager = VBAudioManager(self.config)

        # Initialize VRoid manager
        self.vroid_manager = VRoidManager(self.config)

        # Initialize Translation manager
        self.translation_manager = TranslationManager(self.config)

        # System prompt configuration
        self.system_config = self.config.get("system_prompt", {})

        if self.use_voice_output:
            print("✓ VOICEVOX engine detected - Voice output enabled!")
            # List available speakers
            speakers = self.voicevox.get_speakers()
            if speakers:
                print("Available speakers:")
                for speaker in speakers[:5]:  # Show first 5 speakers
                    print(f"  ID {speaker.get('speaker_uuid', 'N/A')}: {speaker.get('name', 'Unknown')}")

            # Preload common responses for faster TTS
            self._preload_common_responses()
        else:
            print("⚠ VOICEVOX engine not found - Text output only")

        # Adjust for ambient noise
        print("Adjusting for ambient noise... Please wait.")
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        print("Ready to listen!")

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Config file {config_file} not found, using defaults")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing config file: {e}, using defaults")
            return {}

    def _preload_common_responses(self):
        """Preload common responses to reduce latency"""
        # Preload Japanese responses since we'll be using Japanese for TTS
        common_responses = [
            "こんにちは！",
            "はい、わかりました！",
            "ありがとうございます！",
            "またね！バイバイ！",
            "すみません、よくわかりませんでした。",
            "もう一度言ってもらえますか？",
            "それは面白いですね！",
            "そうですね！",
            "翻訳キャッシュがクリアされました！",
            "字幕がクリアされました！",
            "音声キャッシュがクリアされました！"
        ]

        print("🚀 Preloading common Japanese responses...")
        for response in common_responses:
            self.voicevox.preload_audio_query(response)
        print("✓ Common Japanese responses preloaded for faster TTS")

    def listen_for_speech(self) -> Optional[str]:
        """Listen for speech from microphone and return transcribed text"""
        try:
            # Set VRoid to listening state
            self.vroid_manager.set_listening(True)

            # Get speech recognition config
            speech_config = self.config.get("speech_recognition", {})
            language = speech_config.get("language", "id-ID")
            timeout = speech_config.get("timeout", 5)
            phrase_limit = speech_config.get("phrase_time_limit", 10)

            with self.microphone as source:
                print("Listening... Speak now!")
                # Listen for audio with timeout
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_limit)

            print("Processing speech...")
            # Set VRoid to thinking state
            self.vroid_manager.set_thinking(True)

            # Use Google's speech recognition
            text = self.recognizer.recognize_google(audio, language=language)
            print(f"You said: {text}")

            # Add user speech to subtitles
            self.subtitle_manager.add_subtitle(text, speaker="User")

            # Stop thinking animation
            self.vroid_manager.set_thinking(False)

            return text

        except sr.WaitTimeoutError:
            print("No speech detected within timeout period")
            self.vroid_manager.set_listening(False)
            return None
        except sr.UnknownValueError:
            print("Could not understand the audio")
            self.vroid_manager.set_listening(False)
            return None
        except sr.RequestError as e:
            print(f"Error with speech recognition service: {e}")
            self.vroid_manager.set_listening(False)
            return None

    def add_to_history(self, user_input: str, ai_response: str):
        """Add exchange to conversation history"""
        self.conversation_history.append({
            "user": user_input,
            "assistant": ai_response,
            "timestamp": time.time()
        })

        # Keep only recent history
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]

    def get_context_prompt(self, user_input: str) -> str:
        """Create a prompt with conversation history context using system config"""
        # Get system prompt from config
        character_name = self.system_config.get("character_name", "Ai-chan")
        personality = self.system_config.get("personality", "You are a friendly AI vtuber assistant named Ai-chan. Respond in a cheerful, engaging way like a real vtuber would - be enthusiastic, friendly, and helpful. Keep responses conversational and not too long.")

        # Add language and traits
        language = self.system_config.get("language", "id-ID")
        traits = self.system_config.get("vtuber_traits", [])
        max_length = self.system_config.get("max_response_length", 200)

        base_prompt = f"{personality}"
        if traits:
            base_prompt += f" Your personality traits: {', '.join(traits)}."
        base_prompt += f" Respond in {language} language. Keep responses under {max_length} characters."

        if not self.conversation_history:
            return f"{base_prompt}\n\nUser said: {user_input}\n\nRespond as {character_name}:"

        # Build context from recent conversation
        context = f"{base_prompt}\n\nRecent conversation:\n"
        for exchange in self.conversation_history[-3:]:  # Last 3 exchanges for context
            context += f"User: {exchange['user']}\n{character_name}: {exchange['assistant']}\n\n"

        context += f"User: {user_input}\n{character_name}:"
        return context

    def generate_response(self, user_input: str) -> str:
        """Generate AI response using Pollinations API with conversation history and translation"""
        try:
            # Using Pollinations text generation API
            url = "https://text.pollinations.ai/"

            # Create a prompt with conversation context
            prompt = self.get_context_prompt(user_input)

            response = requests.post(url,
                                   json={"messages": [{"role": "user", "content": prompt}]},
                                   headers={"Content-Type": "application/json"},
                                   timeout=15)

            if response.status_code == 200:
                original_response = response.text.strip()
                print(f"Original AI Response: {original_response}")

                # Translate to Japanese
                japanese_translation = self.translation_manager.translate_text(original_response)

                if japanese_translation:
                    # Format as bilingual response
                    bilingual_response = self.translation_manager.format_bilingual_response(
                        original_response, japanese_translation
                    )
                    print(f"Bilingual Response:\n{bilingual_response}")

                    # Add original response to conversation history (not the bilingual format)
                    self.add_to_history(user_input, original_response)

                    return bilingual_response
                else:
                    print("⚠ Translation failed, using original response")
                    # Add to conversation history
                    self.add_to_history(user_input, original_response)
                    return original_response
            else:
                print(f"Error generating response: {response.status_code}")
                return "Sorry, I couldn't generate a response right now!"

        except Exception as e:
            print(f"Error generating response: {e}")
            return "Sorry, I encountered an error while thinking of a response!"

    def speak_response(self, text: str):
        """Speak the response using VOICEVOX with subtitle and VRoid support"""
        # Extract Japanese text for audio output
        japanese_text = self.translation_manager.get_japanese_text(text)

        # Add bilingual subtitle (shows both languages)
        self.subtitle_manager.add_subtitle(text, speaker="Ai-chan")

        # Start VRoid speaking animation with Japanese text
        self.vroid_manager.start_speaking(japanese_text)

        if self.use_voice_output:
            print(f"🔊 Speaking Japanese response: {japanese_text}")
            success = self.voicevox.text_to_speech(japanese_text)
            if not success:
                print("⚠ Voice output failed, falling back to text only")
        else:
            print("📝 Voice output not available")

        # Stop VRoid speaking animation
        self.vroid_manager.stop_speaking()

    def show_conversation_history(self):
        """Display recent conversation history"""
        if not self.conversation_history:
            print("No conversation history yet.")
            return

        print("\n📚 Recent Conversation History:")
        print("=" * 40)
        for i, exchange in enumerate(self.conversation_history[-5:], 1):
            print(f"{i}. User: {exchange['user']}")
            print(f"   Ai-chan: {exchange['assistant']}")
            print("-" * 30)

    def show_voicevox_stats(self):
        """Display VoiceVox performance statistics"""
        if not self.use_voice_output:
            print("VoiceVox is not available")
            return

        stats = self.voicevox.get_cache_stats()
        print("\n🔊 VoiceVox Performance Stats:")
        print("=" * 40)
        print(f"Cache enabled: {stats['cache_enabled']}")
        print(f"Cached queries: {stats['cache_size']}/{stats['max_cache_size']}")
        if stats['cache_size'] > 0:
            print("Recent cached queries:")
            for query in stats['cached_queries'][-5:]:
                text_part = query.split('_')[0][:30]
                print(f"  - {text_part}...")

    def run_conversation_loop(self):
        """Main conversation loop"""
        print("AI Vtuber (Ai-chan) is ready! Press Ctrl+C to stop.")
        if self.use_voice_output:
            print("🔊 Voice output enabled with VOICEVOX")
        print("Say something to start the conversation...")

        try:
            while True:
                # Listen for user input
                user_speech = self.listen_for_speech()

                if user_speech:
                    # Check for special commands
                    user_lower = user_speech.lower()
                    if user_lower in ["show history", "history", "conversation history"]:
                        self.show_conversation_history()
                        continue
                    elif user_lower in ["clear history", "reset history"]:
                        self.conversation_history.clear()
                        response = "Conversation history cleared! Let's start fresh!"
                        print(f"\n🤖 Ai-chan: {response}\n")
                        self.speak_response(response)
                        continue
                    elif user_lower in ["voice stats", "voicevox stats", "performance stats"]:
                        self.show_voicevox_stats()
                        continue
                    elif user_lower in ["clear cache", "clear voice cache"]:
                        if self.use_voice_output:
                            self.voicevox.clear_cache()
                            response = "Voice cache cleared!"
                            print(f"\n🤖 Ai-chan: {response}\n")
                            self.speak_response(response)
                        continue
                    elif user_lower in ["clear subtitles", "clear subs"]:
                        self.subtitle_manager.clear_subtitles()
                        response = "Subtitles cleared!"
                        print(f"\n🤖 Ai-chan: {response}\n")
                        self.speak_response(response)
                        continue
                    elif user_lower in ["vroid stats", "avatar stats"]:
                        self._show_vroid_stats()
                        continue
                    elif user_lower in ["audio stats", "audio info"]:
                        self._show_audio_stats()
                        continue
                    elif user_lower in ["translation stats", "translate stats"]:
                        self._show_translation_stats()
                        continue
                    elif user_lower in ["clear translation cache", "clear translate cache"]:
                        self.translation_manager.clear_cache()
                        response = "Translation cache cleared!"
                        print(f"\n🤖 Ai-chan: {response}\n")
                        self.speak_response(response)
                        continue
                    elif user_lower in ["goodbye", "bye", "exit", "quit"]:
                        farewell = "またね！バイバイ！"
                        print(f"\n🤖 Ai-chan: {farewell}\n")
                        self.speak_response(farewell)
                        break

                    # Generate AI response
                    ai_response = self.generate_response(user_speech)
                    print(f"\n🤖 Ai-chan: {ai_response}\n")

                    # Speak the response if VOICEVOX is available
                    self.speak_response(ai_response)

                    print("-" * 50)

                # Small delay before listening again
                time.sleep(1)

        except KeyboardInterrupt:
            print("\nStopping AI Vtuber. Goodbye!")
            if self.use_voice_output:
                # Say goodbye with voice
                self.voicevox.text_to_speech("またね！バイバイ！")

    def _show_translation_stats(self):
        """Display translation performance statistics"""
        stats = self.translation_manager.get_cache_stats()
        print("\n🌐 Translation Performance Stats:")
        print("=" * 40)
        print(f"Translation enabled: {self.translation_manager.enabled}")
        print(f"Target language: {self.translation_manager.target_language}")
        print(f"Cache enabled: {stats['cache_enabled']}")
        print(f"Cached translations: {stats['cache_size']}/{stats['max_cache_size']}")
        if stats['cache_size'] > 0:
            print("Recent cached translations:")
            for translation in stats['cached_translations'][-5:]:
                text_part = translation.split('_')[0][:30]
                print(f"  - {text_part}...")

    def _show_vroid_stats(self):
        """Display VRoid avatar statistics"""
        print("\n🎭 VRoid Avatar Stats:")
        print("=" * 40)
        print(f"VRoid enabled: {self.vroid_manager.enabled}")
        print(f"Current animation: {self.vroid_manager.current_animation}")
        print(f"Current expression: {self.vroid_manager.current_expression}")
        print(f"Is speaking: {self.vroid_manager.is_speaking}")
        print(f"Is listening: {self.vroid_manager.is_listening}")
        print(f"Is thinking: {self.vroid_manager.is_thinking}")

    def _show_audio_stats(self):
        """Display audio system statistics"""
        print("\n🔊 Audio System Stats:")
        print("=" * 40)
        print(f"VBAudio enabled: {self.vbaudio_manager.enabled}")
        print(f"Voice output available: {self.use_voice_output}")
        if hasattr(self.vbaudio_manager, 'current_device'):
            print(f"Current audio device: {self.vbaudio_manager.current_device}")
        print(f"Microphone initialized: {self.microphone is not None}")
        print(f"Speech recognition language: {self.config.get('speech_recognition', {}).get('language', 'id-ID')}")

def main():
    vtuber = AIVtuber()
    vtuber.run_conversation_loop()

if __name__ == "__main__":
    main()
