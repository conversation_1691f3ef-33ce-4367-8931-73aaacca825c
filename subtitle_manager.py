#!/usr/bin/env python3
"""
Subtitle Manager for OBS Integration
Manages subtitle output for streaming/recording
"""

import os
import time
import threading
from typing import Optional, List, Dict, Any
from datetime import datetime

class SubtitleManager:
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize subtitle manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config.get("subtitles", {})
        self.enabled = self.config.get("enabled", True)
        self.output_file = self.config.get("output_file", "subtitles.txt")
        self.format = self.config.get("format", "simple")
        self.include_timestamps = self.config.get("include_timestamps", True)
        self.max_lines = self.config.get("max_lines", 3)
        self.font_size = self.config.get("font_size", 24)
        self.update_interval = self.config.get("update_interval", 0.1)
        
        # Subtitle history
        self.subtitle_history: List[Dict[str, Any]] = []
        self.current_subtitle = ""
        self.lock = threading.Lock()
        
        # Initialize subtitle file
        if self.enabled:
            self._initialize_subtitle_file()
    
    def _initialize_subtitle_file(self):
        """Initialize the subtitle output file"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                if self.format == "srt":
                    f.write("")  # SRT format will be written as needed
                else:
                    f.write("")  # Simple format starts empty
            print(f"✓ Subtitle file initialized: {self.output_file}")
        except Exception as e:
            print(f"Error initializing subtitle file: {e}")
    
    def add_subtitle(self, text: str, speaker: str = "AI", duration: Optional[float] = None):
        """
        Add a new subtitle entry
        
        Args:
            text: The subtitle text
            speaker: Who is speaking (AI, User, etc.)
            duration: How long to display (auto-calculated if None)
        """
        if not self.enabled:
            return
        
        with self.lock:
            timestamp = datetime.now()
            
            # Calculate duration if not provided (roughly 0.5 seconds per 10 characters)
            if duration is None:
                duration = max(2.0, len(text) * 0.05)
            
            subtitle_entry = {
                "text": text,
                "speaker": speaker,
                "timestamp": timestamp,
                "duration": duration,
                "start_time": time.time()
            }
            
            self.subtitle_history.append(subtitle_entry)
            self.current_subtitle = text
            
            # Keep only recent subtitles
            max_history = 50
            if len(self.subtitle_history) > max_history:
                self.subtitle_history = self.subtitle_history[-max_history:]
            
            # Update subtitle file
            self._update_subtitle_file()
    
    def _update_subtitle_file(self):
        """Update the subtitle file with current content"""
        try:
            if self.format == "srt":
                self._write_srt_format()
            else:
                self._write_simple_format()
        except Exception as e:
            print(f"Error updating subtitle file: {e}")
    
    def _write_simple_format(self):
        """Write subtitles in simple text format for OBS"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            # Get recent active subtitles
            current_time = time.time()
            active_subtitles = []
            
            for entry in self.subtitle_history[-self.max_lines:]:
                elapsed = current_time - entry["start_time"]
                if elapsed < entry["duration"]:
                    active_subtitles.append(entry)
            
            # Write active subtitles
            for entry in active_subtitles[-self.max_lines:]:
                if self.include_timestamps:
                    timestamp_str = entry["timestamp"].strftime("%H:%M:%S")
                    line = f"[{timestamp_str}] {entry['speaker']}: {entry['text']}"
                else:
                    line = f"{entry['speaker']}: {entry['text']}"
                f.write(line + "\n")
    
    def _write_srt_format(self):
        """Write subtitles in SRT format"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            for i, entry in enumerate(self.subtitle_history[-10:], 1):
                start_time = entry["timestamp"]
                end_time_seconds = entry["start_time"] + entry["duration"]
                end_time = datetime.fromtimestamp(end_time_seconds)
                
                # SRT time format: HH:MM:SS,mmm
                start_str = start_time.strftime("%H:%M:%S,") + f"{start_time.microsecond//1000:03d}"
                end_str = end_time.strftime("%H:%M:%S,") + f"{end_time.microsecond//1000:03d}"
                
                f.write(f"{i}\n")
                f.write(f"{start_str} --> {end_str}\n")
                f.write(f"{entry['text']}\n\n")
    
    def clear_subtitles(self):
        """Clear all subtitles"""
        with self.lock:
            self.subtitle_history.clear()
            self.current_subtitle = ""
            
            # Clear the file
            if self.enabled:
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    f.write("")
                print("✓ Subtitles cleared")
    
    def get_current_subtitle(self) -> str:
        """Get the current active subtitle"""
        return self.current_subtitle
    
    def set_enabled(self, enabled: bool):
        """Enable or disable subtitle output"""
        self.enabled = enabled
        if not enabled:
            self.clear_subtitles()
        print(f"✓ Subtitles {'enabled' if enabled else 'disabled'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get subtitle statistics"""
        return {
            "enabled": self.enabled,
            "output_file": self.output_file,
            "total_entries": len(self.subtitle_history),
            "current_subtitle": self.current_subtitle,
            "format": self.format
        }
