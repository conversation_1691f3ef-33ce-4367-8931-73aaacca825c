#!/usr/bin/env python3
"""
VRoid Manager for 3D Avatar Integration
Handles VRoid model animations and expressions
"""

import time
import json
import threading
from typing import Dict, Any, Optional
from enum import Enum

class AnimationState(Enum):
    IDLE = "idle"
    LISTENING = "listening"
    SPEAKING = "speaking"
    THINKING = "thinking"

class ExpressionType(Enum):
    NEUTRAL = "neutral"
    HAPPY = "happy"
    EXCITED = "excited"
    THINKING = "thinking"
    CONFUSED = "confused"

class VRoidManager:
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize VRoid manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config.get("vroid", {})
        self.enabled = self.config.get("enabled", True)
        self.model_path = self.config.get("model_path", "")
        
        # Animation settings
        self.animation_triggers = self.config.get("animation_triggers", {})
        self.expressions = self.config.get("expressions", {})
        self.lip_sync = self.config.get("lip_sync", {})
        
        # Current state
        self.current_animation = AnimationState.IDLE
        self.current_expression = ExpressionType.NEUTRAL
        self.is_speaking = False
        self.lock = threading.Lock()
        
        # Animation output file for external tools
        self.animation_file = "vroid_state.json"
        
        if self.enabled:
            self._initialize_vroid_state()
    
    def _initialize_vroid_state(self):
        """Initialize VRoid state file"""
        try:
            initial_state = {
                "animation": self.current_animation.value,
                "expression": self.current_expression.value,
                "is_speaking": False,
                "lip_sync_data": {
                    "mouth_open": 0.0,
                    "mouth_shape": "neutral"
                },
                "timestamp": time.time()
            }
            
            with open(self.animation_file, 'w', encoding='utf-8') as f:
                json.dump(initial_state, f, indent=2)
            
            print(f"✓ VRoid state initialized: {self.animation_file}")
        except Exception as e:
            print(f"Error initializing VRoid state: {e}")
    
    def set_animation(self, animation: AnimationState):
        """Set current animation state"""
        if not self.enabled:
            return
        
        with self.lock:
            if self.animation_triggers.get(animation.value, True):
                self.current_animation = animation
                self._update_state_file()
                print(f"🎭 Animation: {animation.value}")
    
    def set_expression(self, expression: ExpressionType):
        """Set current facial expression"""
        if not self.enabled:
            return
        
        with self.lock:
            self.current_expression = expression
            self._update_state_file()
            print(f"😊 Expression: {expression.value}")
    
    def start_speaking(self, text: str = ""):
        """Start speaking animation with optional lip sync"""
        if not self.enabled:
            return
        
        with self.lock:
            self.is_speaking = True
            self.current_animation = AnimationState.SPEAKING
            
            # Set happy expression when speaking
            self.current_expression = ExpressionType.HAPPY
            
            self._update_state_file()
            print("🗣️ Started speaking animation")
    
    def stop_speaking(self):
        """Stop speaking animation"""
        if not self.enabled:
            return
        
        with self.lock:
            self.is_speaking = False
            self.current_animation = AnimationState.IDLE
            self.current_expression = ExpressionType.NEUTRAL
            
            self._update_state_file()
            print("🤐 Stopped speaking animation")
    
    def set_listening(self, listening: bool):
        """Set listening state"""
        if not self.enabled:
            return
        
        with self.lock:
            if listening:
                self.current_animation = AnimationState.LISTENING
                self.current_expression = ExpressionType.NEUTRAL
            else:
                self.current_animation = AnimationState.IDLE
                self.current_expression = ExpressionType.NEUTRAL
            
            self._update_state_file()
            status = "listening" if listening else "idle"
            print(f"👂 Animation: {status}")
    
    def set_thinking(self, thinking: bool):
        """Set thinking state"""
        if not self.enabled:
            return
        
        with self.lock:
            if thinking:
                self.current_animation = AnimationState.THINKING
                self.current_expression = ExpressionType.THINKING
            else:
                self.current_animation = AnimationState.IDLE
                self.current_expression = ExpressionType.NEUTRAL
            
            self._update_state_file()
            status = "thinking" if thinking else "idle"
            print(f"🤔 Animation: {status}")
    
    def _update_state_file(self):
        """Update the VRoid state file"""
        try:
            state = {
                "animation": self.current_animation.value,
                "expression": self.current_expression.value,
                "is_speaking": self.is_speaking,
                "lip_sync_data": {
                    "mouth_open": 0.8 if self.is_speaking else 0.0,
                    "mouth_shape": "speaking" if self.is_speaking else "neutral",
                    "enabled": self.lip_sync.get("enabled", True),
                    "sensitivity": self.lip_sync.get("sensitivity", 0.8)
                },
                "timestamp": time.time()
            }
            
            with open(self.animation_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            print(f"Error updating VRoid state: {e}")
    
    def get_current_state(self) -> Dict[str, Any]:
        """Get current VRoid state"""
        return {
            "enabled": self.enabled,
            "animation": self.current_animation.value,
            "expression": self.current_expression.value,
            "is_speaking": self.is_speaking,
            "model_path": self.model_path
        }
    
    def cleanup(self):
        """Cleanup VRoid resources"""
        try:
            self.set_animation(AnimationState.IDLE)
            self.set_expression(ExpressionType.NEUTRAL)
            print("✓ VRoid manager cleaned up")
        except Exception as e:
            print(f"Warning: VRoid cleanup error: {e}")
