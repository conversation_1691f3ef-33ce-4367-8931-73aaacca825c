#!/usr/bin/env python3
"""
Test script to demonstrate the translation functionality
"""

from main import AIVtuber
import json

def test_translation_workflow():
    """Test the complete translation workflow"""
    print("🧪 Testing AI Vtuber Translation Workflow")
    print("=" * 50)
    
    # Initialize the AI Vtuber
    print("1. Initializing AI Vtuber...")
    vtuber = AIVtuber()
    
    # Test translation directly
    print("\n2. Testing direct translation...")
    test_phrases = [
        "Hello, how are you today?",
        "I'm doing great, thank you!",
        "What's your favorite hobby?",
        "I love reading books and watching movies."
    ]
    
    for phrase in test_phrases:
        print(f"\nOriginal: {phrase}")
        translated = vtuber.translation_manager.translate_text(phrase)
        if translated:
            print(f"Japanese: {translated}")
            bilingual = vtuber.translation_manager.format_bilingual_response(phrase, translated)
            print(f"Bilingual format:\n{bilingual}")
            japanese_only = vtuber.translation_manager.get_japanese_text(bilingual)
            print(f"For TTS: {japanese_only}")
        else:
            print("Translation failed")
        print("-" * 30)
    
    # Test response generation with translation
    print("\n3. Testing AI response generation with translation...")
    test_inputs = [
        "Hello there!",
        "How are you feeling today?"
    ]
    
    for user_input in test_inputs:
        print(f"\nUser input: {user_input}")
        try:
            # Generate response (this will include translation)
            response = vtuber.generate_response(user_input)
            print(f"AI Response (bilingual):\n{response}")
            
            # Extract Japanese for TTS
            japanese_for_tts = vtuber.translation_manager.get_japanese_text(response)
            print(f"Japanese for TTS: {japanese_for_tts}")
            
        except Exception as e:
            print(f"Error generating response: {e}")
        print("-" * 30)
    
    # Show translation stats
    print("\n4. Translation Statistics:")
    stats = vtuber.translation_manager.get_cache_stats()
    print(f"Cache size: {stats['cache_size']}/{stats['max_cache_size']}")
    print(f"Cached translations: {len(stats['cached_translations'])}")
    
    print("\n✅ Translation workflow test completed!")

def test_commands():
    """Test translation-related commands"""
    print("\n🎮 Available Translation Commands:")
    print("=" * 40)
    commands = [
        "translation stats - Show translation cache statistics",
        "clear translation cache - Clear the translation cache",
        "translate stats - Alternative command for translation stats",
        "clear translate cache - Alternative command to clear cache"
    ]
    
    for cmd in commands:
        print(f"  • {cmd}")

if __name__ == "__main__":
    try:
        test_translation_workflow()
        test_commands()
        
        print("\n🎯 Summary of Changes:")
        print("=" * 40)
        print("✓ Added TranslationManager class with Google Translate API support")
        print("✓ Modified generate_response() to translate AI responses to Japanese")
        print("✓ Updated speak_response() to use Japanese text for TTS")
        print("✓ Added bilingual display format: 'Jp: [Japanese]\\nEn: [English]'")
        print("✓ Added translation cache for better performance")
        print("✓ Added translation-related voice commands")
        print("✓ Updated config.json with translation settings")
        print("✓ Preloaded common Japanese responses for faster TTS")
        
        print("\n🚀 The AI Vtuber now:")
        print("  • Generates responses in the original language")
        print("  • Translates responses to Japanese")
        print("  • Displays both languages in format: Jp: [Japanese] En: [English]")
        print("  • Uses Japanese text for voice output (TTS)")
        print("  • Caches translations for better performance")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
