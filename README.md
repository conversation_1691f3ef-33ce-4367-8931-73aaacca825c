# AI Vtuber

An AI-powered virtual YouTuber that can listen to your voice, transcribe it, generate responses, and speak back to you.

## Features

- 🎤 **Voice Input**: Listens to your microphone input
- 📝 **Speech Transcription**: Converts speech to text using Google Speech Recognition
- 🤖 **AI Response Generation**: Generates responses using Pollinations AI API
- 🔊 **Text-to-Speech**: (Coming soon with VOICEVOX integration)

## Setup

1. Make sure you have Python 3.12+ installed
2. Install dependencies:
   ```bash
   uv sync
   ```

## Usage

Run the AI vtuber:
```bash
uv run python main.py
```

The application will:
1. Initialize and adjust for ambient noise
2. Start listening for your voice input
3. Transcribe what you say
4. Generate an AI response using the Pollinations API
5. Display the response (voice output coming soon)

Press `Ctrl+C` to stop the application.

## Requirements

- Working microphone
- Internet connection (for speech recognition and AI response generation)
- Python 3.12+

## Dependencies

- `pyaudio`: For microphone input
- `speechrecognition`: For speech-to-text conversion
- `requests`: For API calls to Pollinations
- `pydub`, `numpy`, `soundfile`: For audio processing

## Next Steps

- [ ] Integrate VOICEVOX for text-to-speech output
- [ ] Add visual avatar/character display
- [ ] Improve conversation context and memory
- [ ] Add voice activity detection for better interaction