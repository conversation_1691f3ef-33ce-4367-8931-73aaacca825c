version = 1
revision = 3
requires-python = ">=3.12"
resolution-markers = [
    "python_full_version >= '3.13'",
    "python_full_version < '3.13'",
]

[[package]]
name = "ai-vtuber"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "keyboard" },
    { name = "numpy" },
    { name = "pyaudio" },
    { name = "pydub" },
    { name = "pygame" },
    { name = "requests" },
    { name = "soundfile" },
    { name = "speechrecognition" },
]

[package.metadata]
requires-dist = [
    { name = "keyboard", specifier = ">=0.13.5" },
    { name = "numpy", specifier = ">=2.3.2" },
    { name = "pyaudio", specifier = ">=0.2.14" },
    { name = "pydub", specifier = ">=0.25.1" },
    { name = "pygame", specifier = ">=2.6.1" },
    { name = "requests", specifier = ">=2.32.5" },
    { name = "soundfile", specifier = ">=0.13.1" },
    { name = "speechrecognition", specifier = ">=3.14.3" },
]

[[package]]
name = "audioop-lts"
version = "0.2.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/38/53/946db57842a50b2da2e0c1e34bd37f36f5aadba1a929a3971c5d7841dbca/audioop_lts-0.2.2.tar.gz", hash = "sha256:64d0c62d88e67b98a1a5e71987b7aa7b5bcffc7dcee65b635823dbdd0a8dbbd0", size = 30686, upload-time = "2025-08-05T16:43:17.409Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/de/d4/94d277ca941de5a507b07f0b592f199c22454eeaec8f008a286b3fbbacd6/audioop_lts-0.2.2-cp313-abi3-macosx_10_13_universal2.whl", hash = "sha256:fd3d4602dc64914d462924a08c1a9816435a2155d74f325853c1f1ac3b2d9800", size = 46523, upload-time = "2025-08-05T16:42:20.836Z" },
    { url = "https://files.pythonhosted.org/packages/f8/5a/656d1c2da4b555920ce4177167bfeb8623d98765594af59702c8873f60ec/audioop_lts-0.2.2-cp313-abi3-macosx_10_13_x86_64.whl", hash = "sha256:550c114a8df0aafe9a05442a1162dfc8fec37e9af1d625ae6060fed6e756f303", size = 27455, upload-time = "2025-08-05T16:42:22.283Z" },
    { url = "https://files.pythonhosted.org/packages/1b/83/ea581e364ce7b0d41456fb79d6ee0ad482beda61faf0cab20cbd4c63a541/audioop_lts-0.2.2-cp313-abi3-macosx_11_0_arm64.whl", hash = "sha256:9a13dc409f2564de15dd68be65b462ba0dde01b19663720c68c1140c782d1d75", size = 26997, upload-time = "2025-08-05T16:42:23.849Z" },
    { url = "https://files.pythonhosted.org/packages/b8/3b/e8964210b5e216e5041593b7d33e97ee65967f17c282e8510d19c666dab4/audioop_lts-0.2.2-cp313-abi3-manylinux1_x86_64.manylinux_2_28_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:51c916108c56aa6e426ce611946f901badac950ee2ddaf302b7ed35d9958970d", size = 85844, upload-time = "2025-08-05T16:42:25.208Z" },
    { url = "https://files.pythonhosted.org/packages/c7/2e/0a1c52faf10d51def20531a59ce4c706cb7952323b11709e10de324d6493/audioop_lts-0.2.2-cp313-abi3-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:47eba38322370347b1c47024defbd36374a211e8dd5b0dcbce7b34fdb6f8847b", size = 85056, upload-time = "2025-08-05T16:42:26.559Z" },
    { url = "https://files.pythonhosted.org/packages/75/e8/cd95eef479656cb75ab05dfece8c1f8c395d17a7c651d88f8e6e291a63ab/audioop_lts-0.2.2-cp313-abi3-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:ba7c3a7e5f23e215cb271516197030c32aef2e754252c4c70a50aaff7031a2c8", size = 93892, upload-time = "2025-08-05T16:42:27.902Z" },
    { url = "https://files.pythonhosted.org/packages/5c/1e/a0c42570b74f83efa5cca34905b3eef03f7ab09fe5637015df538a7f3345/audioop_lts-0.2.2-cp313-abi3-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:def246fe9e180626731b26e89816e79aae2276f825420a07b4a647abaa84becc", size = 96660, upload-time = "2025-08-05T16:42:28.9Z" },
    { url = "https://files.pythonhosted.org/packages/50/d5/8a0ae607ca07dbb34027bac8db805498ee7bfecc05fd2c148cc1ed7646e7/audioop_lts-0.2.2-cp313-abi3-manylinux_2_31_riscv64.manylinux_2_39_riscv64.whl", hash = "sha256:e160bf9df356d841bb6c180eeeea1834085464626dc1b68fa4e1d59070affdc3", size = 79143, upload-time = "2025-08-05T16:42:29.929Z" },
    { url = "https://files.pythonhosted.org/packages/12/17/0d28c46179e7910bfb0bb62760ccb33edb5de973052cb2230b662c14ca2e/audioop_lts-0.2.2-cp313-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:4b4cd51a57b698b2d06cb9993b7ac8dfe89a3b2878e96bc7948e9f19ff51dba6", size = 84313, upload-time = "2025-08-05T16:42:30.949Z" },
    { url = "https://files.pythonhosted.org/packages/84/ba/bd5d3806641564f2024e97ca98ea8f8811d4e01d9b9f9831474bc9e14f9e/audioop_lts-0.2.2-cp313-abi3-musllinux_1_2_ppc64le.whl", hash = "sha256:4a53aa7c16a60a6857e6b0b165261436396ef7293f8b5c9c828a3a203147ed4a", size = 93044, upload-time = "2025-08-05T16:42:31.959Z" },
    { url = "https://files.pythonhosted.org/packages/f9/5e/435ce8d5642f1f7679540d1e73c1c42d933331c0976eb397d1717d7f01a3/audioop_lts-0.2.2-cp313-abi3-musllinux_1_2_riscv64.whl", hash = "sha256:3fc38008969796f0f689f1453722a0f463da1b8a6fbee11987830bfbb664f623", size = 78766, upload-time = "2025-08-05T16:42:33.302Z" },
    { url = "https://files.pythonhosted.org/packages/ae/3b/b909e76b606cbfd53875693ec8c156e93e15a1366a012f0b7e4fb52d3c34/audioop_lts-0.2.2-cp313-abi3-musllinux_1_2_s390x.whl", hash = "sha256:15ab25dd3e620790f40e9ead897f91e79c0d3ce65fe193c8ed6c26cffdd24be7", size = 87640, upload-time = "2025-08-05T16:42:34.854Z" },
    { url = "https://files.pythonhosted.org/packages/30/e7/8f1603b4572d79b775f2140d7952f200f5e6c62904585d08a01f0a70393a/audioop_lts-0.2.2-cp313-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:03f061a1915538fd96272bac9551841859dbb2e3bf73ebe4a23ef043766f5449", size = 86052, upload-time = "2025-08-05T16:42:35.839Z" },
    { url = "https://files.pythonhosted.org/packages/b5/96/c37846df657ccdda62ba1ae2b6534fa90e2e1b1742ca8dcf8ebd38c53801/audioop_lts-0.2.2-cp313-abi3-win32.whl", hash = "sha256:3bcddaaf6cc5935a300a8387c99f7a7fbbe212a11568ec6cf6e4bc458c048636", size = 26185, upload-time = "2025-08-05T16:42:37.04Z" },
    { url = "https://files.pythonhosted.org/packages/34/a5/9d78fdb5b844a83da8a71226c7bdae7cc638861085fff7a1d707cb4823fa/audioop_lts-0.2.2-cp313-abi3-win_amd64.whl", hash = "sha256:a2c2a947fae7d1062ef08c4e369e0ba2086049a5e598fda41122535557012e9e", size = 30503, upload-time = "2025-08-05T16:42:38.427Z" },
    { url = "https://files.pythonhosted.org/packages/34/25/20d8fde083123e90c61b51afb547bb0ea7e77bab50d98c0ab243d02a0e43/audioop_lts-0.2.2-cp313-abi3-win_arm64.whl", hash = "sha256:5f93a5db13927a37d2d09637ccca4b2b6b48c19cd9eda7b17a2e9f77edee6a6f", size = 24173, upload-time = "2025-08-05T16:42:39.704Z" },
    { url = "https://files.pythonhosted.org/packages/58/a7/0a764f77b5c4ac58dc13c01a580f5d32ae8c74c92020b961556a43e26d02/audioop_lts-0.2.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:73f80bf4cd5d2ca7814da30a120de1f9408ee0619cc75da87d0641273d202a09", size = 47096, upload-time = "2025-08-05T16:42:40.684Z" },
    { url = "https://files.pythonhosted.org/packages/aa/ed/ebebedde1a18848b085ad0fa54b66ceb95f1f94a3fc04f1cd1b5ccb0ed42/audioop_lts-0.2.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:106753a83a25ee4d6f473f2be6b0966fc1c9af7e0017192f5531a3e7463dce58", size = 27748, upload-time = "2025-08-05T16:42:41.992Z" },
    { url = "https://files.pythonhosted.org/packages/cb/6e/11ca8c21af79f15dbb1c7f8017952ee8c810c438ce4e2b25638dfef2b02c/audioop_lts-0.2.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:fbdd522624141e40948ab3e8cdae6e04c748d78710e9f0f8d4dae2750831de19", size = 27329, upload-time = "2025-08-05T16:42:42.987Z" },
    { url = "https://files.pythonhosted.org/packages/84/52/0022f93d56d85eec5da6b9da6a958a1ef09e80c39f2cc0a590c6af81dcbb/audioop_lts-0.2.2-cp313-cp313t-manylinux1_x86_64.manylinux_2_28_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:143fad0311e8209ece30a8dbddab3b65ab419cbe8c0dde6e8828da25999be911", size = 92407, upload-time = "2025-08-05T16:42:44.336Z" },
    { url = "https://files.pythonhosted.org/packages/87/1d/48a889855e67be8718adbc7a01f3c01d5743c325453a5e81cf3717664aad/audioop_lts-0.2.2-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:dfbbc74ec68a0fd08cfec1f4b5e8cca3d3cd7de5501b01c4b5d209995033cde9", size = 91811, upload-time = "2025-08-05T16:42:45.325Z" },
    { url = "https://files.pythonhosted.org/packages/98/a6/94b7213190e8077547ffae75e13ed05edc488653c85aa5c41472c297d295/audioop_lts-0.2.2-cp313-cp313t-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:cfcac6aa6f42397471e4943e0feb2244549db5c5d01efcd02725b96af417f3fe", size = 100470, upload-time = "2025-08-05T16:42:46.468Z" },
    { url = "https://files.pythonhosted.org/packages/e9/e9/78450d7cb921ede0cfc33426d3a8023a3bda755883c95c868ee36db8d48d/audioop_lts-0.2.2-cp313-cp313t-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:752d76472d9804ac60f0078c79cdae8b956f293177acd2316cd1e15149aee132", size = 103878, upload-time = "2025-08-05T16:42:47.576Z" },
    { url = "https://files.pythonhosted.org/packages/4f/e2/cd5439aad4f3e34ae1ee852025dc6aa8f67a82b97641e390bf7bd9891d3e/audioop_lts-0.2.2-cp313-cp313t-manylinux_2_31_riscv64.manylinux_2_39_riscv64.whl", hash = "sha256:83c381767e2cc10e93e40281a04852facc4cd9334550e0f392f72d1c0a9c5753", size = 84867, upload-time = "2025-08-05T16:42:49.003Z" },
    { url = "https://files.pythonhosted.org/packages/68/4b/9d853e9076c43ebba0d411e8d2aa19061083349ac695a7d082540bad64d0/audioop_lts-0.2.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:c0022283e9556e0f3643b7c3c03f05063ca72b3063291834cca43234f20c60bb", size = 90001, upload-time = "2025-08-05T16:42:50.038Z" },
    { url = "https://files.pythonhosted.org/packages/58/26/4bae7f9d2f116ed5593989d0e521d679b0d583973d203384679323d8fa85/audioop_lts-0.2.2-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:a2d4f1513d63c795e82948e1305f31a6d530626e5f9f2605408b300ae6095093", size = 99046, upload-time = "2025-08-05T16:42:51.111Z" },
    { url = "https://files.pythonhosted.org/packages/b2/67/a9f4fb3e250dda9e9046f8866e9fa7d52664f8985e445c6b4ad6dfb55641/audioop_lts-0.2.2-cp313-cp313t-musllinux_1_2_riscv64.whl", hash = "sha256:c9c8e68d8b4a56fda8c025e538e639f8c5953f5073886b596c93ec9b620055e7", size = 84788, upload-time = "2025-08-05T16:42:52.198Z" },
    { url = "https://files.pythonhosted.org/packages/70/f7/3de86562db0121956148bcb0fe5b506615e3bcf6e63c4357a612b910765a/audioop_lts-0.2.2-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:96f19de485a2925314f5020e85911fb447ff5fbef56e8c7c6927851b95533a1c", size = 94472, upload-time = "2025-08-05T16:42:53.59Z" },
    { url = "https://files.pythonhosted.org/packages/f1/32/fd772bf9078ae1001207d2df1eef3da05bea611a87dd0e8217989b2848fa/audioop_lts-0.2.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:e541c3ef484852ef36545f66209444c48b28661e864ccadb29daddb6a4b8e5f5", size = 92279, upload-time = "2025-08-05T16:42:54.632Z" },
    { url = "https://files.pythonhosted.org/packages/4f/41/affea7181592ab0ab560044632571a38edaf9130b84928177823fbf3176a/audioop_lts-0.2.2-cp313-cp313t-win32.whl", hash = "sha256:d5e73fa573e273e4f2e5ff96f9043858a5e9311e94ffefd88a3186a910c70917", size = 26568, upload-time = "2025-08-05T16:42:55.627Z" },
    { url = "https://files.pythonhosted.org/packages/28/2b/0372842877016641db8fc54d5c88596b542eec2f8f6c20a36fb6612bf9ee/audioop_lts-0.2.2-cp313-cp313t-win_amd64.whl", hash = "sha256:9191d68659eda01e448188f60364c7763a7ca6653ed3f87ebb165822153a8547", size = 30942, upload-time = "2025-08-05T16:42:56.674Z" },
    { url = "https://files.pythonhosted.org/packages/ee/ca/baf2b9cc7e96c179bb4a54f30fcd83e6ecb340031bde68f486403f943768/audioop_lts-0.2.2-cp313-cp313t-win_arm64.whl", hash = "sha256:c174e322bb5783c099aaf87faeb240c8d210686b04bd61dfd05a8e5a83d88969", size = 24603, upload-time = "2025-08-05T16:42:57.571Z" },
    { url = "https://files.pythonhosted.org/packages/5c/73/413b5a2804091e2c7d5def1d618e4837f1cb82464e230f827226278556b7/audioop_lts-0.2.2-cp314-cp314t-macosx_10_13_universal2.whl", hash = "sha256:f9ee9b52f5f857fbaf9d605a360884f034c92c1c23021fb90b2e39b8e64bede6", size = 47104, upload-time = "2025-08-05T16:42:58.518Z" },
    { url = "https://files.pythonhosted.org/packages/ae/8c/daa3308dc6593944410c2c68306a5e217f5c05b70a12e70228e7dd42dc5c/audioop_lts-0.2.2-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:49ee1a41738a23e98d98b937a0638357a2477bc99e61b0f768a8f654f45d9b7a", size = 27754, upload-time = "2025-08-05T16:43:00.132Z" },
    { url = "https://files.pythonhosted.org/packages/4e/86/c2e0f627168fcf61781a8f72cab06b228fe1da4b9fa4ab39cfb791b5836b/audioop_lts-0.2.2-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:5b00be98ccd0fc123dcfad31d50030d25fcf31488cde9e61692029cd7394733b", size = 27332, upload-time = "2025-08-05T16:43:01.666Z" },
    { url = "https://files.pythonhosted.org/packages/c7/bd/35dce665255434f54e5307de39e31912a6f902d4572da7c37582809de14f/audioop_lts-0.2.2-cp314-cp314t-manylinux1_x86_64.manylinux_2_28_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:a6d2e0f9f7a69403e388894d4ca5ada5c47230716a03f2847cfc7bd1ecb589d6", size = 92396, upload-time = "2025-08-05T16:43:02.991Z" },
    { url = "https://files.pythonhosted.org/packages/2d/d2/deeb9f51def1437b3afa35aeb729d577c04bcd89394cb56f9239a9f50b6f/audioop_lts-0.2.2-cp314-cp314t-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:f9b0b8a03ef474f56d1a842af1a2e01398b8f7654009823c6d9e0ecff4d5cfbf", size = 91811, upload-time = "2025-08-05T16:43:04.096Z" },
    { url = "https://files.pythonhosted.org/packages/76/3b/09f8b35b227cee28cc8231e296a82759ed80c1a08e349811d69773c48426/audioop_lts-0.2.2-cp314-cp314t-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:2b267b70747d82125f1a021506565bdc5609a2b24bcb4773c16d79d2bb260bbd", size = 100483, upload-time = "2025-08-05T16:43:05.085Z" },
    { url = "https://files.pythonhosted.org/packages/0b/15/05b48a935cf3b130c248bfdbdea71ce6437f5394ee8533e0edd7cfd93d5e/audioop_lts-0.2.2-cp314-cp314t-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:0337d658f9b81f4cd0fdb1f47635070cc084871a3d4646d9de74fdf4e7c3d24a", size = 103885, upload-time = "2025-08-05T16:43:06.197Z" },
    { url = "https://files.pythonhosted.org/packages/83/80/186b7fce6d35b68d3d739f228dc31d60b3412105854edb975aa155a58339/audioop_lts-0.2.2-cp314-cp314t-manylinux_2_31_riscv64.manylinux_2_39_riscv64.whl", hash = "sha256:167d3b62586faef8b6b2275c3218796b12621a60e43f7e9d5845d627b9c9b80e", size = 84899, upload-time = "2025-08-05T16:43:07.291Z" },
    { url = "https://files.pythonhosted.org/packages/49/89/c78cc5ac6cb5828f17514fb12966e299c850bc885e80f8ad94e38d450886/audioop_lts-0.2.2-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:0d9385e96f9f6da847f4d571ce3cb15b5091140edf3db97276872647ce37efd7", size = 89998, upload-time = "2025-08-05T16:43:08.335Z" },
    { url = "https://files.pythonhosted.org/packages/4c/4b/6401888d0c010e586c2ca50fce4c903d70a6bb55928b16cfbdfd957a13da/audioop_lts-0.2.2-cp314-cp314t-musllinux_1_2_ppc64le.whl", hash = "sha256:48159d96962674eccdca9a3df280e864e8ac75e40a577cc97c5c42667ffabfc5", size = 99046, upload-time = "2025-08-05T16:43:09.367Z" },
    { url = "https://files.pythonhosted.org/packages/de/f8/c874ca9bb447dae0e2ef2e231f6c4c2b0c39e31ae684d2420b0f9e97ee68/audioop_lts-0.2.2-cp314-cp314t-musllinux_1_2_riscv64.whl", hash = "sha256:8fefe5868cd082db1186f2837d64cfbfa78b548ea0d0543e9b28935ccce81ce9", size = 84843, upload-time = "2025-08-05T16:43:10.749Z" },
    { url = "https://files.pythonhosted.org/packages/3e/c0/0323e66f3daebc13fd46b36b30c3be47e3fc4257eae44f1e77eb828c703f/audioop_lts-0.2.2-cp314-cp314t-musllinux_1_2_s390x.whl", hash = "sha256:58cf54380c3884fb49fdd37dfb7a772632b6701d28edd3e2904743c5e1773602", size = 94490, upload-time = "2025-08-05T16:43:12.131Z" },
    { url = "https://files.pythonhosted.org/packages/98/6b/acc7734ac02d95ab791c10c3f17ffa3584ccb9ac5c18fd771c638ed6d1f5/audioop_lts-0.2.2-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:088327f00488cdeed296edd9215ca159f3a5a5034741465789cad403fcf4bec0", size = 92297, upload-time = "2025-08-05T16:43:13.139Z" },
    { url = "https://files.pythonhosted.org/packages/13/c3/c3dc3f564ce6877ecd2a05f8d751b9b27a8c320c2533a98b0c86349778d0/audioop_lts-0.2.2-cp314-cp314t-win32.whl", hash = "sha256:068aa17a38b4e0e7de771c62c60bbca2455924b67a8814f3b0dee92b5820c0b3", size = 27331, upload-time = "2025-08-05T16:43:14.19Z" },
    { url = "https://files.pythonhosted.org/packages/72/bb/b4608537e9ffcb86449091939d52d24a055216a36a8bf66b936af8c3e7ac/audioop_lts-0.2.2-cp314-cp314t-win_amd64.whl", hash = "sha256:a5bf613e96f49712073de86f20dbdd4014ca18efd4d34ed18c75bd808337851b", size = 31697, upload-time = "2025-08-05T16:43:15.193Z" },
    { url = "https://files.pythonhosted.org/packages/f6/22/91616fe707a5c5510de2cac9b046a30defe7007ba8a0c04f9c08f27df312/audioop_lts-0.2.2-cp314-cp314t-win_arm64.whl", hash = "sha256:b492c3b040153e68b9fdaff5913305aaaba5bb433d8a7f73d5cf6a64ed3cc1dd", size = 25206, upload-time = "2025-08-05T16:43:16.444Z" },
]

[[package]]
name = "certifi"
version = "2025.8.3"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/dc/67/960ebe6bf230a96cda2e0abcf73af550ec4f090005363542f0765df162e0/certifi-2025.8.3.tar.gz", hash = "sha256:e564105f78ded564e3ae7c923924435e1daa7463faeab5bb932bc53ffae63407", size = 162386, upload-time = "2025-08-03T03:07:47.08Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl", hash = "sha256:f6c12493cfb1b06ba2ff328595af9350c65d6644968e5d3a2ffd78699af217a5", size = 161216, upload-time = "2025-08-03T03:07:45.777Z" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fc/97/c783634659c2920c3fc70419e3af40972dbaf758daa229a7d6ea6135c90d/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824", size = 516621, upload-time = "2024-09-04T20:45:21.852Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5a/84/e94227139ee5fb4d600a7a4927f322e1d4aea6fdc50bd3fca8493caba23f/cffi-1.17.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:805b4371bf7197c329fcb3ead37e710d1bca9da5d583f5073b799d5c5bd1eee4", size = 183178, upload-time = "2024-09-04T20:44:12.232Z" },
    { url = "https://files.pythonhosted.org/packages/da/ee/fb72c2b48656111c4ef27f0f91da355e130a923473bf5ee75c5643d00cca/cffi-1.17.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:733e99bc2df47476e3848417c5a4540522f234dfd4ef3ab7fafdf555b082ec0c", size = 178840, upload-time = "2024-09-04T20:44:13.739Z" },
    { url = "https://files.pythonhosted.org/packages/cc/b6/db007700f67d151abadf508cbfd6a1884f57eab90b1bb985c4c8c02b0f28/cffi-1.17.1-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1257bdabf294dceb59f5e70c64a3e2f462c30c7ad68092d01bbbfb1c16b1ba36", size = 454803, upload-time = "2024-09-04T20:44:15.231Z" },
    { url = "https://files.pythonhosted.org/packages/1a/df/f8d151540d8c200eb1c6fba8cd0dfd40904f1b0682ea705c36e6c2e97ab3/cffi-1.17.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:da95af8214998d77a98cc14e3a3bd00aa191526343078b530ceb0bd710fb48a5", size = 478850, upload-time = "2024-09-04T20:44:17.188Z" },
    { url = "https://files.pythonhosted.org/packages/28/c0/b31116332a547fd2677ae5b78a2ef662dfc8023d67f41b2a83f7c2aa78b1/cffi-1.17.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d63afe322132c194cf832bfec0dc69a99fb9bb6bbd550f161a49e9e855cc78ff", size = 485729, upload-time = "2024-09-04T20:44:18.688Z" },
    { url = "https://files.pythonhosted.org/packages/91/2b/9a1ddfa5c7f13cab007a2c9cc295b70fbbda7cb10a286aa6810338e60ea1/cffi-1.17.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f79fc4fc25f1c8698ff97788206bb3c2598949bfe0fef03d299eb1b5356ada99", size = 471256, upload-time = "2024-09-04T20:44:20.248Z" },
    { url = "https://files.pythonhosted.org/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93", size = 479424, upload-time = "2024-09-04T20:44:21.673Z" },
    { url = "https://files.pythonhosted.org/packages/0b/ac/2a28bcf513e93a219c8a4e8e125534f4f6db03e3179ba1c45e949b76212c/cffi-1.17.1-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:386c8bf53c502fff58903061338ce4f4950cbdcb23e2902d86c0f722b786bbe3", size = 484568, upload-time = "2024-09-04T20:44:23.245Z" },
    { url = "https://files.pythonhosted.org/packages/d4/38/ca8a4f639065f14ae0f1d9751e70447a261f1a30fa7547a828ae08142465/cffi-1.17.1-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:4ceb10419a9adf4460ea14cfd6bc43d08701f0835e979bf821052f1805850fe8", size = 488736, upload-time = "2024-09-04T20:44:24.757Z" },
    { url = "https://files.pythonhosted.org/packages/86/c5/28b2d6f799ec0bdecf44dced2ec5ed43e0eb63097b0f58c293583b406582/cffi-1.17.1-cp312-cp312-win32.whl", hash = "sha256:a08d7e755f8ed21095a310a693525137cfe756ce62d066e53f502a83dc550f65", size = 172448, upload-time = "2024-09-04T20:44:26.208Z" },
    { url = "https://files.pythonhosted.org/packages/50/b9/db34c4755a7bd1cb2d1603ac3863f22bcecbd1ba29e5ee841a4bc510b294/cffi-1.17.1-cp312-cp312-win_amd64.whl", hash = "sha256:51392eae71afec0d0c8fb1a53b204dbb3bcabcb3c9b807eedf3e1e6ccf2de903", size = 181976, upload-time = "2024-09-04T20:44:27.578Z" },
    { url = "https://files.pythonhosted.org/packages/8d/f8/dd6c246b148639254dad4d6803eb6a54e8c85c6e11ec9df2cffa87571dbe/cffi-1.17.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f3a2b4222ce6b60e2e8b337bb9596923045681d71e5a082783484d845390938e", size = 182989, upload-time = "2024-09-04T20:44:28.956Z" },
    { url = "https://files.pythonhosted.org/packages/8b/f1/672d303ddf17c24fc83afd712316fda78dc6fce1cd53011b839483e1ecc8/cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0984a4925a435b1da406122d4d7968dd861c1385afe3b45ba82b750f229811e2", size = 178802, upload-time = "2024-09-04T20:44:30.289Z" },
    { url = "https://files.pythonhosted.org/packages/0e/2d/eab2e858a91fdff70533cab61dcff4a1f55ec60425832ddfdc9cd36bc8af/cffi-1.17.1-cp313-cp313-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d01b12eeeb4427d3110de311e1774046ad344f5b1a7403101878976ecd7a10f3", size = 454792, upload-time = "2024-09-04T20:44:32.01Z" },
    { url = "https://files.pythonhosted.org/packages/75/b2/fbaec7c4455c604e29388d55599b99ebcc250a60050610fadde58932b7ee/cffi-1.17.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:706510fe141c86a69c8ddc029c7910003a17353970cff3b904ff0686a5927683", size = 478893, upload-time = "2024-09-04T20:44:33.606Z" },
    { url = "https://files.pythonhosted.org/packages/4f/b7/6e4a2162178bf1935c336d4da8a9352cccab4d3a5d7914065490f08c0690/cffi-1.17.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:de55b766c7aa2e2a3092c51e0483d700341182f08e67c63630d5b6f200bb28e5", size = 485810, upload-time = "2024-09-04T20:44:35.191Z" },
    { url = "https://files.pythonhosted.org/packages/c7/8a/1d0e4a9c26e54746dc08c2c6c037889124d4f59dffd853a659fa545f1b40/cffi-1.17.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c59d6e989d07460165cc5ad3c61f9fd8f1b4796eacbd81cee78957842b834af4", size = 471200, upload-time = "2024-09-04T20:44:36.743Z" },
    { url = "https://files.pythonhosted.org/packages/26/9f/1aab65a6c0db35f43c4d1b4f580e8df53914310afc10ae0397d29d697af4/cffi-1.17.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:dd398dbc6773384a17fe0d3e7eeb8d1a21c2200473ee6806bb5e6a8e62bb73dd", size = 479447, upload-time = "2024-09-04T20:44:38.492Z" },
    { url = "https://files.pythonhosted.org/packages/5f/e4/fb8b3dd8dc0e98edf1135ff067ae070bb32ef9d509d6cb0f538cd6f7483f/cffi-1.17.1-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:3edc8d958eb099c634dace3c7e16560ae474aa3803a5df240542b305d14e14ed", size = 484358, upload-time = "2024-09-04T20:44:40.046Z" },
    { url = "https://files.pythonhosted.org/packages/f1/47/d7145bf2dc04684935d57d67dff9d6d795b2ba2796806bb109864be3a151/cffi-1.17.1-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:72e72408cad3d5419375fc87d289076ee319835bdfa2caad331e377589aebba9", size = 488469, upload-time = "2024-09-04T20:44:41.616Z" },
    { url = "https://files.pythonhosted.org/packages/bf/ee/f94057fa6426481d663b88637a9a10e859e492c73d0384514a17d78ee205/cffi-1.17.1-cp313-cp313-win32.whl", hash = "sha256:e03eab0a8677fa80d646b5ddece1cbeaf556c313dcfac435ba11f107ba117b5d", size = 172475, upload-time = "2024-09-04T20:44:43.733Z" },
    { url = "https://files.pythonhosted.org/packages/7c/fc/6a8cb64e5f0324877d503c854da15d76c1e50eb722e320b15345c4d0c6de/cffi-1.17.1-cp313-cp313-win_amd64.whl", hash = "sha256:f6a16c31041f09ead72d69f583767292f750d24913dadacf5756b966aacb3f1a", size = 182009, upload-time = "2024-09-04T20:44:45.309Z" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.3"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/83/2d/5fd176ceb9b2fc619e63405525573493ca23441330fcdaee6bef9460e924/charset_normalizer-3.4.3.tar.gz", hash = "sha256:6fce4b8500244f6fcb71465d4a4930d132ba9ab8e71a7859e6a5d59851068d14", size = 122371, upload-time = "2025-08-09T07:57:28.46Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e9/5e/14c94999e418d9b87682734589404a25854d5f5d0408df68bc15b6ff54bb/charset_normalizer-3.4.3-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:e28e334d3ff134e88989d90ba04b47d84382a828c061d0d1027b1b12a62b39b1", size = 205655, upload-time = "2025-08-09T07:56:08.475Z" },
    { url = "https://files.pythonhosted.org/packages/7d/a8/c6ec5d389672521f644505a257f50544c074cf5fc292d5390331cd6fc9c3/charset_normalizer-3.4.3-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:0cacf8f7297b0c4fcb74227692ca46b4a5852f8f4f24b3c766dd94a1075c4884", size = 146223, upload-time = "2025-08-09T07:56:09.708Z" },
    { url = "https://files.pythonhosted.org/packages/fc/eb/a2ffb08547f4e1e5415fb69eb7db25932c52a52bed371429648db4d84fb1/charset_normalizer-3.4.3-cp312-cp312-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:c6fd51128a41297f5409deab284fecbe5305ebd7e5a1f959bee1c054622b7018", size = 159366, upload-time = "2025-08-09T07:56:11.326Z" },
    { url = "https://files.pythonhosted.org/packages/82/10/0fd19f20c624b278dddaf83b8464dcddc2456cb4b02bb902a6da126b87a1/charset_normalizer-3.4.3-cp312-cp312-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:3cfb2aad70f2c6debfbcb717f23b7eb55febc0bb23dcffc0f076009da10c6392", size = 157104, upload-time = "2025-08-09T07:56:13.014Z" },
    { url = "https://files.pythonhosted.org/packages/16/ab/0233c3231af734f5dfcf0844aa9582d5a1466c985bbed6cedab85af9bfe3/charset_normalizer-3.4.3-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:1606f4a55c0fd363d754049cdf400175ee96c992b1f8018b993941f221221c5f", size = 151830, upload-time = "2025-08-09T07:56:14.428Z" },
    { url = "https://files.pythonhosted.org/packages/ae/02/e29e22b4e02839a0e4a06557b1999d0a47db3567e82989b5bb21f3fbbd9f/charset_normalizer-3.4.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:027b776c26d38b7f15b26a5da1044f376455fb3766df8fc38563b4efbc515154", size = 148854, upload-time = "2025-08-09T07:56:16.051Z" },
    { url = "https://files.pythonhosted.org/packages/05/6b/e2539a0a4be302b481e8cafb5af8792da8093b486885a1ae4d15d452bcec/charset_normalizer-3.4.3-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:42e5088973e56e31e4fa58eb6bd709e42fc03799c11c42929592889a2e54c491", size = 160670, upload-time = "2025-08-09T07:56:17.314Z" },
    { url = "https://files.pythonhosted.org/packages/31/e7/883ee5676a2ef217a40ce0bffcc3d0dfbf9e64cbcfbdf822c52981c3304b/charset_normalizer-3.4.3-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:cc34f233c9e71701040d772aa7490318673aa7164a0efe3172b2981218c26d93", size = 158501, upload-time = "2025-08-09T07:56:18.641Z" },
    { url = "https://files.pythonhosted.org/packages/c1/35/6525b21aa0db614cf8b5792d232021dca3df7f90a1944db934efa5d20bb1/charset_normalizer-3.4.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:320e8e66157cc4e247d9ddca8e21f427efc7a04bbd0ac8a9faf56583fa543f9f", size = 153173, upload-time = "2025-08-09T07:56:20.289Z" },
    { url = "https://files.pythonhosted.org/packages/50/ee/f4704bad8201de513fdc8aac1cabc87e38c5818c93857140e06e772b5892/charset_normalizer-3.4.3-cp312-cp312-win32.whl", hash = "sha256:fb6fecfd65564f208cbf0fba07f107fb661bcd1a7c389edbced3f7a493f70e37", size = 99822, upload-time = "2025-08-09T07:56:21.551Z" },
    { url = "https://files.pythonhosted.org/packages/39/f5/3b3836ca6064d0992c58c7561c6b6eee1b3892e9665d650c803bd5614522/charset_normalizer-3.4.3-cp312-cp312-win_amd64.whl", hash = "sha256:86df271bf921c2ee3818f0522e9a5b8092ca2ad8b065ece5d7d9d0e9f4849bcc", size = 107543, upload-time = "2025-08-09T07:56:23.115Z" },
    { url = "https://files.pythonhosted.org/packages/65/ca/2135ac97709b400c7654b4b764daf5c5567c2da45a30cdd20f9eefe2d658/charset_normalizer-3.4.3-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:14c2a87c65b351109f6abfc424cab3927b3bdece6f706e4d12faaf3d52ee5efe", size = 205326, upload-time = "2025-08-09T07:56:24.721Z" },
    { url = "https://files.pythonhosted.org/packages/71/11/98a04c3c97dd34e49c7d247083af03645ca3730809a5509443f3c37f7c99/charset_normalizer-3.4.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:41d1fc408ff5fdfb910200ec0e74abc40387bccb3252f3f27c0676731df2b2c8", size = 146008, upload-time = "2025-08-09T07:56:26.004Z" },
    { url = "https://files.pythonhosted.org/packages/60/f5/4659a4cb3c4ec146bec80c32d8bb16033752574c20b1252ee842a95d1a1e/charset_normalizer-3.4.3-cp313-cp313-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:1bb60174149316da1c35fa5233681f7c0f9f514509b8e399ab70fea5f17e45c9", size = 159196, upload-time = "2025-08-09T07:56:27.25Z" },
    { url = "https://files.pythonhosted.org/packages/86/9e/f552f7a00611f168b9a5865a1414179b2c6de8235a4fa40189f6f79a1753/charset_normalizer-3.4.3-cp313-cp313-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:30d006f98569de3459c2fc1f2acde170b7b2bd265dc1943e87e1a4efe1b67c31", size = 156819, upload-time = "2025-08-09T07:56:28.515Z" },
    { url = "https://files.pythonhosted.org/packages/7e/95/42aa2156235cbc8fa61208aded06ef46111c4d3f0de233107b3f38631803/charset_normalizer-3.4.3-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:416175faf02e4b0810f1f38bcb54682878a4af94059a1cd63b8747244420801f", size = 151350, upload-time = "2025-08-09T07:56:29.716Z" },
    { url = "https://files.pythonhosted.org/packages/c2/a9/3865b02c56f300a6f94fc631ef54f0a8a29da74fb45a773dfd3dcd380af7/charset_normalizer-3.4.3-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:6aab0f181c486f973bc7262a97f5aca3ee7e1437011ef0c2ec04b5a11d16c927", size = 148644, upload-time = "2025-08-09T07:56:30.984Z" },
    { url = "https://files.pythonhosted.org/packages/77/d9/cbcf1a2a5c7d7856f11e7ac2d782aec12bdfea60d104e60e0aa1c97849dc/charset_normalizer-3.4.3-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:fdabf8315679312cfa71302f9bd509ded4f2f263fb5b765cf1433b39106c3cc9", size = 160468, upload-time = "2025-08-09T07:56:32.252Z" },
    { url = "https://files.pythonhosted.org/packages/f6/42/6f45efee8697b89fda4d50580f292b8f7f9306cb2971d4b53f8914e4d890/charset_normalizer-3.4.3-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:bd28b817ea8c70215401f657edef3a8aa83c29d447fb0b622c35403780ba11d5", size = 158187, upload-time = "2025-08-09T07:56:33.481Z" },
    { url = "https://files.pythonhosted.org/packages/70/99/f1c3bdcfaa9c45b3ce96f70b14f070411366fa19549c1d4832c935d8e2c3/charset_normalizer-3.4.3-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:18343b2d246dc6761a249ba1fb13f9ee9a2bcd95decc767319506056ea4ad4dc", size = 152699, upload-time = "2025-08-09T07:56:34.739Z" },
    { url = "https://files.pythonhosted.org/packages/a3/ad/b0081f2f99a4b194bcbb1934ef3b12aa4d9702ced80a37026b7607c72e58/charset_normalizer-3.4.3-cp313-cp313-win32.whl", hash = "sha256:6fb70de56f1859a3f71261cbe41005f56a7842cc348d3aeb26237560bfa5e0ce", size = 99580, upload-time = "2025-08-09T07:56:35.981Z" },
    { url = "https://files.pythonhosted.org/packages/9a/8f/ae790790c7b64f925e5c953b924aaa42a243fb778fed9e41f147b2a5715a/charset_normalizer-3.4.3-cp313-cp313-win_amd64.whl", hash = "sha256:cf1ebb7d78e1ad8ec2a8c4732c7be2e736f6e5123a4146c5b89c9d1f585f8cef", size = 107366, upload-time = "2025-08-09T07:56:37.339Z" },
    { url = "https://files.pythonhosted.org/packages/8e/91/b5a06ad970ddc7a0e513112d40113e834638f4ca1120eb727a249fb2715e/charset_normalizer-3.4.3-cp314-cp314-macosx_10_13_universal2.whl", hash = "sha256:3cd35b7e8aedeb9e34c41385fda4f73ba609e561faedfae0a9e75e44ac558a15", size = 204342, upload-time = "2025-08-09T07:56:38.687Z" },
    { url = "https://files.pythonhosted.org/packages/ce/ec/1edc30a377f0a02689342f214455c3f6c2fbedd896a1d2f856c002fc3062/charset_normalizer-3.4.3-cp314-cp314-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:b89bc04de1d83006373429975f8ef9e7932534b8cc9ca582e4db7d20d91816db", size = 145995, upload-time = "2025-08-09T07:56:40.048Z" },
    { url = "https://files.pythonhosted.org/packages/17/e5/5e67ab85e6d22b04641acb5399c8684f4d37caf7558a53859f0283a650e9/charset_normalizer-3.4.3-cp314-cp314-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:2001a39612b241dae17b4687898843f254f8748b796a2e16f1051a17078d991d", size = 158640, upload-time = "2025-08-09T07:56:41.311Z" },
    { url = "https://files.pythonhosted.org/packages/f1/e5/38421987f6c697ee3722981289d554957c4be652f963d71c5e46a262e135/charset_normalizer-3.4.3-cp314-cp314-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:8dcfc373f888e4fb39a7bc57e93e3b845e7f462dacc008d9749568b1c4ece096", size = 156636, upload-time = "2025-08-09T07:56:43.195Z" },
    { url = "https://files.pythonhosted.org/packages/a0/e4/5a075de8daa3ec0745a9a3b54467e0c2967daaaf2cec04c845f73493e9a1/charset_normalizer-3.4.3-cp314-cp314-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:18b97b8404387b96cdbd30ad660f6407799126d26a39ca65729162fd810a99aa", size = 150939, upload-time = "2025-08-09T07:56:44.819Z" },
    { url = "https://files.pythonhosted.org/packages/02/f7/3611b32318b30974131db62b4043f335861d4d9b49adc6d57c1149cc49d4/charset_normalizer-3.4.3-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:ccf600859c183d70eb47e05a44cd80a4ce77394d1ac0f79dbd2dd90a69a3a049", size = 148580, upload-time = "2025-08-09T07:56:46.684Z" },
    { url = "https://files.pythonhosted.org/packages/7e/61/19b36f4bd67f2793ab6a99b979b4e4f3d8fc754cbdffb805335df4337126/charset_normalizer-3.4.3-cp314-cp314-musllinux_1_2_ppc64le.whl", hash = "sha256:53cd68b185d98dde4ad8990e56a58dea83a4162161b1ea9272e5c9182ce415e0", size = 159870, upload-time = "2025-08-09T07:56:47.941Z" },
    { url = "https://files.pythonhosted.org/packages/06/57/84722eefdd338c04cf3030ada66889298eaedf3e7a30a624201e0cbe424a/charset_normalizer-3.4.3-cp314-cp314-musllinux_1_2_s390x.whl", hash = "sha256:30a96e1e1f865f78b030d65241c1ee850cdf422d869e9028e2fc1d5e4db73b92", size = 157797, upload-time = "2025-08-09T07:56:49.756Z" },
    { url = "https://files.pythonhosted.org/packages/72/2a/aff5dd112b2f14bcc3462c312dce5445806bfc8ab3a7328555da95330e4b/charset_normalizer-3.4.3-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:d716a916938e03231e86e43782ca7878fb602a125a91e7acb8b5112e2e96ac16", size = 152224, upload-time = "2025-08-09T07:56:51.369Z" },
    { url = "https://files.pythonhosted.org/packages/b7/8c/9839225320046ed279c6e839d51f028342eb77c91c89b8ef2549f951f3ec/charset_normalizer-3.4.3-cp314-cp314-win32.whl", hash = "sha256:c6dbd0ccdda3a2ba7c2ecd9d77b37f3b5831687d8dc1b6ca5f56a4880cc7b7ce", size = 100086, upload-time = "2025-08-09T07:56:52.722Z" },
    { url = "https://files.pythonhosted.org/packages/ee/7a/36fbcf646e41f710ce0a563c1c9a343c6edf9be80786edeb15b6f62e17db/charset_normalizer-3.4.3-cp314-cp314-win_amd64.whl", hash = "sha256:73dc19b562516fc9bcf6e5d6e596df0b4eb98d87e4f79f3ae71840e6ed21361c", size = 107400, upload-time = "2025-08-09T07:56:55.172Z" },
    { url = "https://files.pythonhosted.org/packages/8a/1f/f041989e93b001bc4e44bb1669ccdcf54d3f00e628229a85b08d330615c5/charset_normalizer-3.4.3-py3-none-any.whl", hash = "sha256:ce571ab16d890d23b5c278547ba694193a45011ff86a9162a71307ed9f86759a", size = 53175, upload-time = "2025-08-09T07:57:26.864Z" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", size = 190490, upload-time = "2024-09-15T18:07:39.745Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", size = 70442, upload-time = "2024-09-15T18:07:37.964Z" },
]

[[package]]
name = "keyboard"
version = "0.13.5"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/75/c969f2258e908c39aadfc57d1cb78247dc49e6d36371bb3a48c194640c01/keyboard-0.13.5.zip", hash = "sha256:63ed83305955939ca5c9a73755e5cc43e8242263f5ad5fd3bb7e0b032f3d308b", size = 71798, upload-time = "2020-03-23T21:47:06.614Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/55/88/287159903c5b3fc6d47b651c7ab65a54dcf9c9916de546188a7f62870d6d/keyboard-0.13.5-py3-none-any.whl", hash = "sha256:8e9c2422f1217e0bd84489b9ecd361027cc78415828f4fe4f88dd4acd587947b", size = 58098, upload-time = "2020-03-23T21:47:05.023Z" },
]

[[package]]
name = "numpy"
version = "2.3.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/37/7d/3fec4199c5ffb892bed55cff901e4f39a58c81df9c44c280499e92cad264/numpy-2.3.2.tar.gz", hash = "sha256:e0486a11ec30cdecb53f184d496d1c6a20786c81e55e41640270130056f8ee48", size = 20489306, upload-time = "2025-07-24T21:32:07.553Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/00/6d/745dd1c1c5c284d17725e5c802ca4d45cfc6803519d777f087b71c9f4069/numpy-2.3.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:bc3186bea41fae9d8e90c2b4fb5f0a1f5a690682da79b92574d63f56b529080b", size = 20956420, upload-time = "2025-07-24T20:28:18.002Z" },
    { url = "https://files.pythonhosted.org/packages/bc/96/e7b533ea5740641dd62b07a790af5d9d8fec36000b8e2d0472bd7574105f/numpy-2.3.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:2f4f0215edb189048a3c03bd5b19345bdfa7b45a7a6f72ae5945d2a28272727f", size = 14184660, upload-time = "2025-07-24T20:28:39.522Z" },
    { url = "https://files.pythonhosted.org/packages/2b/53/102c6122db45a62aa20d1b18c9986f67e6b97e0d6fbc1ae13e3e4c84430c/numpy-2.3.2-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:8b1224a734cd509f70816455c3cffe13a4f599b1bf7130f913ba0e2c0b2006c0", size = 5113382, upload-time = "2025-07-24T20:28:48.544Z" },
    { url = "https://files.pythonhosted.org/packages/2b/21/376257efcbf63e624250717e82b4fae93d60178f09eb03ed766dbb48ec9c/numpy-2.3.2-cp312-cp312-macosx_14_0_x86_64.whl", hash = "sha256:3dcf02866b977a38ba3ec10215220609ab9667378a9e2150615673f3ffd6c73b", size = 6647258, upload-time = "2025-07-24T20:28:59.104Z" },
    { url = "https://files.pythonhosted.org/packages/91/ba/f4ebf257f08affa464fe6036e13f2bf9d4642a40228781dc1235da81be9f/numpy-2.3.2-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:572d5512df5470f50ada8d1972c5f1082d9a0b7aa5944db8084077570cf98370", size = 14281409, upload-time = "2025-07-24T20:40:30.298Z" },
    { url = "https://files.pythonhosted.org/packages/59/ef/f96536f1df42c668cbacb727a8c6da7afc9c05ece6d558927fb1722693e1/numpy-2.3.2-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:8145dd6d10df13c559d1e4314df29695613575183fa2e2d11fac4c208c8a1f73", size = 16641317, upload-time = "2025-07-24T20:40:56.625Z" },
    { url = "https://files.pythonhosted.org/packages/f6/a7/af813a7b4f9a42f498dde8a4c6fcbff8100eed00182cc91dbaf095645f38/numpy-2.3.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:103ea7063fa624af04a791c39f97070bf93b96d7af7eb23530cd087dc8dbe9dc", size = 16056262, upload-time = "2025-07-24T20:41:20.797Z" },
    { url = "https://files.pythonhosted.org/packages/8b/5d/41c4ef8404caaa7f05ed1cfb06afe16a25895260eacbd29b4d84dff2920b/numpy-2.3.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:fc927d7f289d14f5e037be917539620603294454130b6de200091e23d27dc9be", size = 18579342, upload-time = "2025-07-24T20:41:50.753Z" },
    { url = "https://files.pythonhosted.org/packages/a1/4f/9950e44c5a11636f4a3af6e825ec23003475cc9a466edb7a759ed3ea63bd/numpy-2.3.2-cp312-cp312-win32.whl", hash = "sha256:d95f59afe7f808c103be692175008bab926b59309ade3e6d25009e9a171f7036", size = 6320610, upload-time = "2025-07-24T20:42:01.551Z" },
    { url = "https://files.pythonhosted.org/packages/7c/2f/244643a5ce54a94f0a9a2ab578189c061e4a87c002e037b0829dd77293b6/numpy-2.3.2-cp312-cp312-win_amd64.whl", hash = "sha256:9e196ade2400c0c737d93465327d1ae7c06c7cb8a1756121ebf54b06ca183c7f", size = 12786292, upload-time = "2025-07-24T20:42:20.738Z" },
    { url = "https://files.pythonhosted.org/packages/54/cd/7b5f49d5d78db7badab22d8323c1b6ae458fbf86c4fdfa194ab3cd4eb39b/numpy-2.3.2-cp312-cp312-win_arm64.whl", hash = "sha256:ee807923782faaf60d0d7331f5e86da7d5e3079e28b291973c545476c2b00d07", size = 10194071, upload-time = "2025-07-24T20:42:36.657Z" },
    { url = "https://files.pythonhosted.org/packages/1c/c0/c6bb172c916b00700ed3bf71cb56175fd1f7dbecebf8353545d0b5519f6c/numpy-2.3.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:c8d9727f5316a256425892b043736d63e89ed15bbfe6556c5ff4d9d4448ff3b3", size = 20949074, upload-time = "2025-07-24T20:43:07.813Z" },
    { url = "https://files.pythonhosted.org/packages/20/4e/c116466d22acaf4573e58421c956c6076dc526e24a6be0903219775d862e/numpy-2.3.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:efc81393f25f14d11c9d161e46e6ee348637c0a1e8a54bf9dedc472a3fae993b", size = 14177311, upload-time = "2025-07-24T20:43:29.335Z" },
    { url = "https://files.pythonhosted.org/packages/78/45/d4698c182895af189c463fc91d70805d455a227261d950e4e0f1310c2550/numpy-2.3.2-cp313-cp313-macosx_14_0_arm64.whl", hash = "sha256:dd937f088a2df683cbb79dda9a772b62a3e5a8a7e76690612c2737f38c6ef1b6", size = 5106022, upload-time = "2025-07-24T20:43:37.999Z" },
    { url = "https://files.pythonhosted.org/packages/9f/76/3e6880fef4420179309dba72a8c11f6166c431cf6dee54c577af8906f914/numpy-2.3.2-cp313-cp313-macosx_14_0_x86_64.whl", hash = "sha256:11e58218c0c46c80509186e460d79fbdc9ca1eb8d8aee39d8f2dc768eb781089", size = 6640135, upload-time = "2025-07-24T20:43:49.28Z" },
    { url = "https://files.pythonhosted.org/packages/34/fa/87ff7f25b3c4ce9085a62554460b7db686fef1e0207e8977795c7b7d7ba1/numpy-2.3.2-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:5ad4ebcb683a1f99f4f392cc522ee20a18b2bb12a2c1c42c3d48d5a1adc9d3d2", size = 14278147, upload-time = "2025-07-24T20:44:10.328Z" },
    { url = "https://files.pythonhosted.org/packages/1d/0f/571b2c7a3833ae419fe69ff7b479a78d313581785203cc70a8db90121b9a/numpy-2.3.2-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:938065908d1d869c7d75d8ec45f735a034771c6ea07088867f713d1cd3bbbe4f", size = 16635989, upload-time = "2025-07-24T20:44:34.88Z" },
    { url = "https://files.pythonhosted.org/packages/24/5a/84ae8dca9c9a4c592fe11340b36a86ffa9fd3e40513198daf8a97839345c/numpy-2.3.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:66459dccc65d8ec98cc7df61307b64bf9e08101f9598755d42d8ae65d9a7a6ee", size = 16053052, upload-time = "2025-07-24T20:44:58.872Z" },
    { url = "https://files.pythonhosted.org/packages/57/7c/e5725d99a9133b9813fcf148d3f858df98511686e853169dbaf63aec6097/numpy-2.3.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:a7af9ed2aa9ec5950daf05bb11abc4076a108bd3c7db9aa7251d5f107079b6a6", size = 18577955, upload-time = "2025-07-24T20:45:26.714Z" },
    { url = "https://files.pythonhosted.org/packages/ae/11/7c546fcf42145f29b71e4d6f429e96d8d68e5a7ba1830b2e68d7418f0bbd/numpy-2.3.2-cp313-cp313-win32.whl", hash = "sha256:906a30249315f9c8e17b085cc5f87d3f369b35fedd0051d4a84686967bdbbd0b", size = 6311843, upload-time = "2025-07-24T20:49:24.444Z" },
    { url = "https://files.pythonhosted.org/packages/aa/6f/a428fd1cb7ed39b4280d057720fed5121b0d7754fd2a9768640160f5517b/numpy-2.3.2-cp313-cp313-win_amd64.whl", hash = "sha256:c63d95dc9d67b676e9108fe0d2182987ccb0f11933c1e8959f42fa0da8d4fa56", size = 12782876, upload-time = "2025-07-24T20:49:43.227Z" },
    { url = "https://files.pythonhosted.org/packages/65/85/4ea455c9040a12595fb6c43f2c217257c7b52dd0ba332c6a6c1d28b289fe/numpy-2.3.2-cp313-cp313-win_arm64.whl", hash = "sha256:b05a89f2fb84d21235f93de47129dd4f11c16f64c87c33f5e284e6a3a54e43f2", size = 10192786, upload-time = "2025-07-24T20:49:59.443Z" },
    { url = "https://files.pythonhosted.org/packages/80/23/8278f40282d10c3f258ec3ff1b103d4994bcad78b0cba9208317f6bb73da/numpy-2.3.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:4e6ecfeddfa83b02318f4d84acf15fbdbf9ded18e46989a15a8b6995dfbf85ab", size = 21047395, upload-time = "2025-07-24T20:45:58.821Z" },
    { url = "https://files.pythonhosted.org/packages/1f/2d/624f2ce4a5df52628b4ccd16a4f9437b37c35f4f8a50d00e962aae6efd7a/numpy-2.3.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:508b0eada3eded10a3b55725b40806a4b855961040180028f52580c4729916a2", size = 14300374, upload-time = "2025-07-24T20:46:20.207Z" },
    { url = "https://files.pythonhosted.org/packages/f6/62/ff1e512cdbb829b80a6bd08318a58698867bca0ca2499d101b4af063ee97/numpy-2.3.2-cp313-cp313t-macosx_14_0_arm64.whl", hash = "sha256:754d6755d9a7588bdc6ac47dc4ee97867271b17cee39cb87aef079574366db0a", size = 5228864, upload-time = "2025-07-24T20:46:30.58Z" },
    { url = "https://files.pythonhosted.org/packages/7d/8e/74bc18078fff03192d4032cfa99d5a5ca937807136d6f5790ce07ca53515/numpy-2.3.2-cp313-cp313t-macosx_14_0_x86_64.whl", hash = "sha256:a9f66e7d2b2d7712410d3bc5684149040ef5f19856f20277cd17ea83e5006286", size = 6737533, upload-time = "2025-07-24T20:46:46.111Z" },
    { url = "https://files.pythonhosted.org/packages/19/ea/0731efe2c9073ccca5698ef6a8c3667c4cf4eea53fcdcd0b50140aba03bc/numpy-2.3.2-cp313-cp313t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:de6ea4e5a65d5a90c7d286ddff2b87f3f4ad61faa3db8dabe936b34c2275b6f8", size = 14352007, upload-time = "2025-07-24T20:47:07.1Z" },
    { url = "https://files.pythonhosted.org/packages/cf/90/36be0865f16dfed20f4bc7f75235b963d5939707d4b591f086777412ff7b/numpy-2.3.2-cp313-cp313t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:a3ef07ec8cbc8fc9e369c8dcd52019510c12da4de81367d8b20bc692aa07573a", size = 16701914, upload-time = "2025-07-24T20:47:32.459Z" },
    { url = "https://files.pythonhosted.org/packages/94/30/06cd055e24cb6c38e5989a9e747042b4e723535758e6153f11afea88c01b/numpy-2.3.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:27c9f90e7481275c7800dc9c24b7cc40ace3fdb970ae4d21eaff983a32f70c91", size = 16132708, upload-time = "2025-07-24T20:47:58.129Z" },
    { url = "https://files.pythonhosted.org/packages/9a/14/ecede608ea73e58267fd7cb78f42341b3b37ba576e778a1a06baffbe585c/numpy-2.3.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:07b62978075b67eee4065b166d000d457c82a1efe726cce608b9db9dd66a73a5", size = 18651678, upload-time = "2025-07-24T20:48:25.402Z" },
    { url = "https://files.pythonhosted.org/packages/40/f3/2fe6066b8d07c3685509bc24d56386534c008b462a488b7f503ba82b8923/numpy-2.3.2-cp313-cp313t-win32.whl", hash = "sha256:c771cfac34a4f2c0de8e8c97312d07d64fd8f8ed45bc9f5726a7e947270152b5", size = 6441832, upload-time = "2025-07-24T20:48:37.181Z" },
    { url = "https://files.pythonhosted.org/packages/0b/ba/0937d66d05204d8f28630c9c60bc3eda68824abde4cf756c4d6aad03b0c6/numpy-2.3.2-cp313-cp313t-win_amd64.whl", hash = "sha256:72dbebb2dcc8305c431b2836bcc66af967df91be793d63a24e3d9b741374c450", size = 12927049, upload-time = "2025-07-24T20:48:56.24Z" },
    { url = "https://files.pythonhosted.org/packages/e9/ed/13542dd59c104d5e654dfa2ac282c199ba64846a74c2c4bcdbc3a0f75df1/numpy-2.3.2-cp313-cp313t-win_arm64.whl", hash = "sha256:72c6df2267e926a6d5286b0a6d556ebe49eae261062059317837fda12ddf0c1a", size = 10262935, upload-time = "2025-07-24T20:49:13.136Z" },
    { url = "https://files.pythonhosted.org/packages/c9/7c/7659048aaf498f7611b783e000c7268fcc4dcf0ce21cd10aad7b2e8f9591/numpy-2.3.2-cp314-cp314-macosx_10_13_x86_64.whl", hash = "sha256:448a66d052d0cf14ce9865d159bfc403282c9bc7bb2a31b03cc18b651eca8b1a", size = 20950906, upload-time = "2025-07-24T20:50:30.346Z" },
    { url = "https://files.pythonhosted.org/packages/80/db/984bea9d4ddf7112a04cfdfb22b1050af5757864cfffe8e09e44b7f11a10/numpy-2.3.2-cp314-cp314-macosx_11_0_arm64.whl", hash = "sha256:546aaf78e81b4081b2eba1d105c3b34064783027a06b3ab20b6eba21fb64132b", size = 14185607, upload-time = "2025-07-24T20:50:51.923Z" },
    { url = "https://files.pythonhosted.org/packages/e4/76/b3d6f414f4eca568f469ac112a3b510938d892bc5a6c190cb883af080b77/numpy-2.3.2-cp314-cp314-macosx_14_0_arm64.whl", hash = "sha256:87c930d52f45df092f7578889711a0768094debf73cfcde105e2d66954358125", size = 5114110, upload-time = "2025-07-24T20:51:01.041Z" },
    { url = "https://files.pythonhosted.org/packages/9e/d2/6f5e6826abd6bca52392ed88fe44a4b52aacb60567ac3bc86c67834c3a56/numpy-2.3.2-cp314-cp314-macosx_14_0_x86_64.whl", hash = "sha256:8dc082ea901a62edb8f59713c6a7e28a85daddcb67454c839de57656478f5b19", size = 6642050, upload-time = "2025-07-24T20:51:11.64Z" },
    { url = "https://files.pythonhosted.org/packages/c4/43/f12b2ade99199e39c73ad182f103f9d9791f48d885c600c8e05927865baf/numpy-2.3.2-cp314-cp314-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:af58de8745f7fa9ca1c0c7c943616c6fe28e75d0c81f5c295810e3c83b5be92f", size = 14296292, upload-time = "2025-07-24T20:51:33.488Z" },
    { url = "https://files.pythonhosted.org/packages/5d/f9/77c07d94bf110a916b17210fac38680ed8734c236bfed9982fd8524a7b47/numpy-2.3.2-cp314-cp314-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:fed5527c4cf10f16c6d0b6bee1f89958bccb0ad2522c8cadc2efd318bcd545f5", size = 16638913, upload-time = "2025-07-24T20:51:58.517Z" },
    { url = "https://files.pythonhosted.org/packages/9b/d1/9d9f2c8ea399cc05cfff8a7437453bd4e7d894373a93cdc46361bbb49a7d/numpy-2.3.2-cp314-cp314-musllinux_1_2_aarch64.whl", hash = "sha256:095737ed986e00393ec18ec0b21b47c22889ae4b0cd2d5e88342e08b01141f58", size = 16071180, upload-time = "2025-07-24T20:52:22.827Z" },
    { url = "https://files.pythonhosted.org/packages/4c/41/82e2c68aff2a0c9bf315e47d61951099fed65d8cb2c8d9dc388cb87e947e/numpy-2.3.2-cp314-cp314-musllinux_1_2_x86_64.whl", hash = "sha256:b5e40e80299607f597e1a8a247ff8d71d79c5b52baa11cc1cce30aa92d2da6e0", size = 18576809, upload-time = "2025-07-24T20:52:51.015Z" },
    { url = "https://files.pythonhosted.org/packages/14/14/4b4fd3efb0837ed252d0f583c5c35a75121038a8c4e065f2c259be06d2d8/numpy-2.3.2-cp314-cp314-win32.whl", hash = "sha256:7d6e390423cc1f76e1b8108c9b6889d20a7a1f59d9a60cac4a050fa734d6c1e2", size = 6366410, upload-time = "2025-07-24T20:56:44.949Z" },
    { url = "https://files.pythonhosted.org/packages/11/9e/b4c24a6b8467b61aced5c8dc7dcfce23621baa2e17f661edb2444a418040/numpy-2.3.2-cp314-cp314-win_amd64.whl", hash = "sha256:b9d0878b21e3918d76d2209c924ebb272340da1fb51abc00f986c258cd5e957b", size = 12918821, upload-time = "2025-07-24T20:57:06.479Z" },
    { url = "https://files.pythonhosted.org/packages/0e/0f/0dc44007c70b1007c1cef86b06986a3812dd7106d8f946c09cfa75782556/numpy-2.3.2-cp314-cp314-win_arm64.whl", hash = "sha256:2738534837c6a1d0c39340a190177d7d66fdf432894f469728da901f8f6dc910", size = 10477303, upload-time = "2025-07-24T20:57:22.879Z" },
    { url = "https://files.pythonhosted.org/packages/8b/3e/075752b79140b78ddfc9c0a1634d234cfdbc6f9bbbfa6b7504e445ad7d19/numpy-2.3.2-cp314-cp314t-macosx_10_13_x86_64.whl", hash = "sha256:4d002ecf7c9b53240be3bb69d80f86ddbd34078bae04d87be81c1f58466f264e", size = 21047524, upload-time = "2025-07-24T20:53:22.086Z" },
    { url = "https://files.pythonhosted.org/packages/fe/6d/60e8247564a72426570d0e0ea1151b95ce5bd2f1597bb878a18d32aec855/numpy-2.3.2-cp314-cp314t-macosx_11_0_arm64.whl", hash = "sha256:293b2192c6bcce487dbc6326de5853787f870aeb6c43f8f9c6496db5b1781e45", size = 14300519, upload-time = "2025-07-24T20:53:44.053Z" },
    { url = "https://files.pythonhosted.org/packages/4d/73/d8326c442cd428d47a067070c3ac6cc3b651a6e53613a1668342a12d4479/numpy-2.3.2-cp314-cp314t-macosx_14_0_arm64.whl", hash = "sha256:0a4f2021a6da53a0d580d6ef5db29947025ae8b35b3250141805ea9a32bbe86b", size = 5228972, upload-time = "2025-07-24T20:53:53.81Z" },
    { url = "https://files.pythonhosted.org/packages/34/2e/e71b2d6dad075271e7079db776196829019b90ce3ece5c69639e4f6fdc44/numpy-2.3.2-cp314-cp314t-macosx_14_0_x86_64.whl", hash = "sha256:9c144440db4bf3bb6372d2c3e49834cc0ff7bb4c24975ab33e01199e645416f2", size = 6737439, upload-time = "2025-07-24T20:54:04.742Z" },
    { url = "https://files.pythonhosted.org/packages/15/b0/d004bcd56c2c5e0500ffc65385eb6d569ffd3363cb5e593ae742749b2daa/numpy-2.3.2-cp314-cp314t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:f92d6c2a8535dc4fe4419562294ff957f83a16ebdec66df0805e473ffaad8bd0", size = 14352479, upload-time = "2025-07-24T20:54:25.819Z" },
    { url = "https://files.pythonhosted.org/packages/11/e3/285142fcff8721e0c99b51686426165059874c150ea9ab898e12a492e291/numpy-2.3.2-cp314-cp314t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:cefc2219baa48e468e3db7e706305fcd0c095534a192a08f31e98d83a7d45fb0", size = 16702805, upload-time = "2025-07-24T20:54:50.814Z" },
    { url = "https://files.pythonhosted.org/packages/33/c3/33b56b0e47e604af2c7cd065edca892d180f5899599b76830652875249a3/numpy-2.3.2-cp314-cp314t-musllinux_1_2_aarch64.whl", hash = "sha256:76c3e9501ceb50b2ff3824c3589d5d1ab4ac857b0ee3f8f49629d0de55ecf7c2", size = 16133830, upload-time = "2025-07-24T20:55:17.306Z" },
    { url = "https://files.pythonhosted.org/packages/6e/ae/7b1476a1f4d6a48bc669b8deb09939c56dd2a439db1ab03017844374fb67/numpy-2.3.2-cp314-cp314t-musllinux_1_2_x86_64.whl", hash = "sha256:122bf5ed9a0221b3419672493878ba4967121514b1d7d4656a7580cd11dddcbf", size = 18652665, upload-time = "2025-07-24T20:55:46.665Z" },
    { url = "https://files.pythonhosted.org/packages/14/ba/5b5c9978c4bb161034148ade2de9db44ec316fab89ce8c400db0e0c81f86/numpy-2.3.2-cp314-cp314t-win32.whl", hash = "sha256:6f1ae3dcb840edccc45af496f312528c15b1f79ac318169d094e85e4bb35fdf1", size = 6514777, upload-time = "2025-07-24T20:55:57.66Z" },
    { url = "https://files.pythonhosted.org/packages/eb/46/3dbaf0ae7c17cdc46b9f662c56da2054887b8d9e737c1476f335c83d33db/numpy-2.3.2-cp314-cp314t-win_amd64.whl", hash = "sha256:087ffc25890d89a43536f75c5fe8770922008758e8eeeef61733957041ed2f9b", size = 13111856, upload-time = "2025-07-24T20:56:17.318Z" },
    { url = "https://files.pythonhosted.org/packages/c1/9e/1652778bce745a67b5fe05adde60ed362d38eb17d919a540e813d30f6874/numpy-2.3.2-cp314-cp314t-win_arm64.whl", hash = "sha256:092aeb3449833ea9c0bf0089d70c29ae480685dd2377ec9cdbbb620257f84631", size = 10544226, upload-time = "2025-07-24T20:56:34.509Z" },
]

[[package]]
name = "pyaudio"
version = "0.2.14"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/26/1d/8878c7752febb0f6716a7e1a52cb92ac98871c5aa522cba181878091607c/PyAudio-0.2.14.tar.gz", hash = "sha256:78dfff3879b4994d1f4fc6485646a57755c6ee3c19647a491f790a0895bd2f87", size = 47066, upload-time = "2023-11-07T07:11:48.806Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8d/45/8d2b76e8f6db783f9326c1305f3f816d4a12c8eda5edc6a2e1d03c097c3b/PyAudio-0.2.14-cp312-cp312-win32.whl", hash = "sha256:5fce4bcdd2e0e8c063d835dbe2860dac46437506af509353c7f8114d4bacbd5b", size = 144750, upload-time = "2023-11-07T07:11:40.142Z" },
    { url = "https://files.pythonhosted.org/packages/b0/6a/d25812e5f79f06285767ec607b39149d02aa3b31d50c2269768f48768930/PyAudio-0.2.14-cp312-cp312-win_amd64.whl", hash = "sha256:12f2f1ba04e06ff95d80700a78967897a489c05e093e3bffa05a84ed9c0a7fa3", size = 164126, upload-time = "2023-11-07T07:11:41.539Z" },
    { url = "https://files.pythonhosted.org/packages/3a/77/66cd37111a87c1589b63524f3d3c848011d21ca97828422c7fde7665ff0d/PyAudio-0.2.14-cp313-cp313-win32.whl", hash = "sha256:95328285b4dab57ea8c52a4a996cb52be6d629353315be5bfda403d15932a497", size = 150982, upload-time = "2024-11-20T19:12:12.404Z" },
    { url = "https://files.pythonhosted.org/packages/a5/8b/7f9a061c1cc2b230f9ac02a6003fcd14c85ce1828013aecbaf45aa988d20/PyAudio-0.2.14-cp313-cp313-win_amd64.whl", hash = "sha256:692d8c1446f52ed2662120bcd9ddcb5aa2b71f38bda31e58b19fb4672fffba69", size = 173655, upload-time = "2024-11-20T19:12:13.616Z" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/1d/b2/31537cf4b1ca988837256c910a668b553fceb8f069bedc4b1c826024b52c/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6", size = 172736, upload-time = "2024-03-30T13:22:22.564Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", size = 117552, upload-time = "2024-03-30T13:22:20.476Z" },
]

[[package]]
name = "pydub"
version = "0.25.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/fe/9a/e6bca0eed82db26562c73b5076539a4a08d3cffd19c3cc5913a3e61145fd/pydub-0.25.1.tar.gz", hash = "sha256:980a33ce9949cab2a569606b65674d748ecbca4f0796887fd6f46173a7b0d30f", size = 38326, upload-time = "2021-03-10T02:09:54.659Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a6/53/d78dc063216e62fc55f6b2eebb447f6a4b0a59f55c8406376f76bf959b08/pydub-0.25.1-py2.py3-none-any.whl", hash = "sha256:65617e33033874b59d87db603aa1ed450633288aefead953b30bded59cb599a6", size = 32327, upload-time = "2021-03-10T02:09:53.503Z" },
]

[[package]]
name = "pygame"
version = "2.6.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/49/cc/08bba60f00541f62aaa252ce0cfbd60aebd04616c0b9574f755b583e45ae/pygame-2.6.1.tar.gz", hash = "sha256:56fb02ead529cee00d415c3e007f75e0780c655909aaa8e8bf616ee09c9feb1f", size = 14808125, upload-time = "2024-09-29T13:41:34.698Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/92/16/2c602c332f45ff9526d61f6bd764db5096ff9035433e2172e2d2cadae8db/pygame-2.6.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:4ee7f2771f588c966fa2fa8b829be26698c9b4836f82ede5e4edc1a68594942e", size = 13118279, upload-time = "2024-09-29T14:26:30.427Z" },
    { url = "https://files.pythonhosted.org/packages/cd/53/77ccbc384b251c6e34bfd2e734c638233922449a7844e3c7a11ef91cee39/pygame-2.6.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:c8040ea2ab18c6b255af706ec01355c8a6b08dc48d77fd4ee783f8fc46a843bf", size = 12384524, upload-time = "2024-09-29T14:26:49.996Z" },
    { url = "https://files.pythonhosted.org/packages/06/be/3ed337583f010696c3b3435e89a74fb29d0c74d0931e8f33c0a4246307a9/pygame-2.6.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c47a6938de93fa610accd4969e638c2aebcb29b2fca518a84c3a39d91ab47116", size = 13587123, upload-time = "2024-09-29T11:10:50.072Z" },
    { url = "https://files.pythonhosted.org/packages/fd/ca/b015586a450db59313535662991b34d24c1f0c0dc149cc5f496573900f4e/pygame-2.6.1-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:33006f784e1c7d7e466fcb61d5489da59cc5f7eb098712f792a225df1d4e229d", size = 14275532, upload-time = "2024-09-29T11:39:59.356Z" },
    { url = "https://files.pythonhosted.org/packages/b9/f2/d31e6ad42d657af07be2ffd779190353f759a07b51232b9e1d724f2cda46/pygame-2.6.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1206125f14cae22c44565c9d333607f1d9f59487b1f1432945dfc809aeaa3e88", size = 13952653, upload-time = "2024-09-29T11:40:01.781Z" },
    { url = "https://files.pythonhosted.org/packages/f3/42/8ea2a6979e6fa971702fece1747e862e2256d4a8558fe0da6364dd946c53/pygame-2.6.1-cp312-cp312-win32.whl", hash = "sha256:84fc4054e25262140d09d39e094f6880d730199710829902f0d8ceae0213379e", size = 10252421, upload-time = "2024-09-29T11:14:26.877Z" },
    { url = "https://files.pythonhosted.org/packages/5f/90/7d766d54bb95939725e9a9361f9c06b0cfbe3fe100aa35400f0a461a278a/pygame-2.6.1-cp312-cp312-win_amd64.whl", hash = "sha256:3a9e7396be0d9633831c3f8d5d82dd63ba373ad65599628294b7a4f8a5a01a65", size = 10624591, upload-time = "2024-09-29T11:52:54.489Z" },
    { url = "https://files.pythonhosted.org/packages/e1/91/718acf3e2a9d08a6ddcc96bd02a6f63c99ee7ba14afeaff2a51c987df0b9/pygame-2.6.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:ae6039f3a55d800db80e8010f387557b528d34d534435e0871326804df2a62f2", size = 13090765, upload-time = "2024-09-29T14:27:02.377Z" },
    { url = "https://files.pythonhosted.org/packages/0e/c6/9cb315de851a7682d9c7568a41ea042ee98d668cb8deadc1dafcab6116f0/pygame-2.6.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:2a3a1288e2e9b1e5834e425bedd5ba01a3cd4902b5c2bff8ed4a740ccfe98171", size = 12381704, upload-time = "2024-09-29T14:27:10.228Z" },
    { url = "https://files.pythonhosted.org/packages/9f/8f/617a1196e31ae3b46be6949fbaa95b8c93ce15e0544266198c2266cc1b4d/pygame-2.6.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:27eb17e3dc9640e4b4683074f1890e2e879827447770470c2aba9f125f74510b", size = 13581091, upload-time = "2024-09-29T11:30:27.653Z" },
    { url = "https://files.pythonhosted.org/packages/3b/87/2851a564e40a2dad353f1c6e143465d445dab18a95281f9ea458b94f3608/pygame-2.6.1-cp313-cp313-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4c1623180e70a03c4a734deb9bac50fc9c82942ae84a3a220779062128e75f3b", size = 14273844, upload-time = "2024-09-29T11:40:04.138Z" },
    { url = "https://files.pythonhosted.org/packages/85/b5/aa23aa2e70bcba42c989c02e7228273c30f3b44b9b264abb93eaeff43ad7/pygame-2.6.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ef07c0103d79492c21fced9ad68c11c32efa6801ca1920ebfd0f15fb46c78b1c", size = 13951197, upload-time = "2024-09-29T11:40:06.785Z" },
    { url = "https://files.pythonhosted.org/packages/a6/06/29e939b34d3f1354738c7d201c51c250ad7abefefaf6f8332d962ff67c4b/pygame-2.6.1-cp313-cp313-win32.whl", hash = "sha256:3acd8c009317190c2bfd81db681ecef47d5eb108c2151d09596d9c7ea9df5c0e", size = 10249309, upload-time = "2024-09-29T11:10:23.329Z" },
    { url = "https://files.pythonhosted.org/packages/7e/11/17f7f319ca91824b86557e9303e3b7a71991ef17fd45286bf47d7f0a38e6/pygame-2.6.1-cp313-cp313-win_amd64.whl", hash = "sha256:813af4fba5d0b2cb8e58f5d95f7910295c34067dcc290d34f1be59c48bd1ea6a", size = ********, upload-time = "2024-09-29T11:48:51.587Z" },
]

[[package]]
name = "pyobjc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accessibility", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-accounts", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-addressbook" },
    { name = "pyobjc-framework-adservices", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-adsupport", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-applescriptkit" },
    { name = "pyobjc-framework-applescriptobjc", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-applicationservices" },
    { name = "pyobjc-framework-apptrackingtransparency", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-audiovideobridging", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-authenticationservices", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automaticassessmentconfiguration", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-automator" },
    { name = "pyobjc-framework-avfoundation", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-avkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-avrouting", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-backgroundassets", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-browserenginekit", marker = "platform_release >= '23.4'" },
    { name = "pyobjc-framework-businesschat", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-calendarstore", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-callkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-carbon" },
    { name = "pyobjc-framework-cfnetwork" },
    { name = "pyobjc-framework-cinematic", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-classkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-cloudkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-collaboration", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-colorsync", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-contacts", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-contactsui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coreaudiokit" },
    { name = "pyobjc-framework-corebluetooth", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corehaptics", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-corelocation", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-coremedia", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremediaio", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-coremidi" },
    { name = "pyobjc-framework-coreml", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coremotion", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-coreservices" },
    { name = "pyobjc-framework-corespotlight", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-corewlan", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-cryptotokenkit", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-datadetection", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-devicecheck", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-devicediscoveryextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-dictionaryservices", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-discrecording" },
    { name = "pyobjc-framework-discrecordingui" },
    { name = "pyobjc-framework-diskarbitration" },
    { name = "pyobjc-framework-dvdplayback" },
    { name = "pyobjc-framework-eventkit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-exceptionhandling" },
    { name = "pyobjc-framework-executionpolicy", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-extensionkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-externalaccessory", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-fileprovider", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-fileproviderui", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-findersync", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-fsevents", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-fskit", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-gamecenter", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gamecontroller", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-gamekit", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-gameplaykit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-healthkit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-imagecapturecore", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-inputmethodkit", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-installerplugins" },
    { name = "pyobjc-framework-instantmessage", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-intents", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-intentsui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-iobluetooth" },
    { name = "pyobjc-framework-iobluetoothui" },
    { name = "pyobjc-framework-iosurface", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-ituneslibrary", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-kernelmanagement", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-latentsemanticmapping" },
    { name = "pyobjc-framework-launchservices" },
    { name = "pyobjc-framework-libdispatch", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-libxpc", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-linkpresentation", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-localauthentication", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-localauthenticationembeddedui", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mailkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mapkit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaaccessibility", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaextension", marker = "platform_release >= '24.0'" },
    { name = "pyobjc-framework-medialibrary", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-mediaplayer", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-mediatoolbox", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-metal", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalfx", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-metalkit", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-metalperformanceshaders", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-metalperformanceshadersgraph", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-metrickit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-mlcompute", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-modelio", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-multipeerconnectivity", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-naturallanguage", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-netfs", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-network", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-networkextension", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-notificationcenter", marker = "platform_release >= '14.0'" },
    { name = "pyobjc-framework-opendirectory", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-osakit" },
    { name = "pyobjc-framework-oslog", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-passkit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-pencilkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-phase", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-photos", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-photosui", marker = "platform_release >= '15.0'" },
    { name = "pyobjc-framework-preferencepanes" },
    { name = "pyobjc-framework-pushkit", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-quartz" },
    { name = "pyobjc-framework-quicklookthumbnailing", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-replaykit", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-safariservices", marker = "platform_release >= '16.0'" },
    { name = "pyobjc-framework-safetykit", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-scenekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-screencapturekit", marker = "platform_release >= '21.4'" },
    { name = "pyobjc-framework-screensaver" },
    { name = "pyobjc-framework-screentime", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-scriptingbridge", marker = "platform_release >= '9.0'" },
    { name = "pyobjc-framework-searchkit" },
    { name = "pyobjc-framework-security" },
    { name = "pyobjc-framework-securityfoundation" },
    { name = "pyobjc-framework-securityinterface" },
    { name = "pyobjc-framework-securityui", marker = "platform_release >= '24.4'" },
    { name = "pyobjc-framework-sensitivecontentanalysis", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-servicemanagement", marker = "platform_release >= '10.0'" },
    { name = "pyobjc-framework-sharedwithyou", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-sharedwithyoucore", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-shazamkit", marker = "platform_release >= '21.0'" },
    { name = "pyobjc-framework-social", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-soundanalysis", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-speech", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-spritekit", marker = "platform_release >= '13.0'" },
    { name = "pyobjc-framework-storekit", marker = "platform_release >= '11.0'" },
    { name = "pyobjc-framework-symbols", marker = "platform_release >= '23.0'" },
    { name = "pyobjc-framework-syncservices" },
    { name = "pyobjc-framework-systemconfiguration" },
    { name = "pyobjc-framework-systemextensions", marker = "platform_release >= '19.0'" },
    { name = "pyobjc-framework-threadnetwork", marker = "platform_release >= '22.0'" },
    { name = "pyobjc-framework-uniformtypeidentifiers", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-usernotifications", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-usernotificationsui", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-videosubscriberaccount", marker = "platform_release >= '18.0'" },
    { name = "pyobjc-framework-videotoolbox", marker = "platform_release >= '12.0'" },
    { name = "pyobjc-framework-virtualization", marker = "platform_release >= '20.0'" },
    { name = "pyobjc-framework-vision", marker = "platform_release >= '17.0'" },
    { name = "pyobjc-framework-webkit" },
]
sdist = { url = "https://files.pythonhosted.org/packages/db/5e/16bc372806790d295c76b5c7851767cc9ee3787b3e581f5d7cc44158e4e0/pyobjc-11.1.tar.gz", hash = "sha256:a71b14389657811d658526ba4d5faba4ef7eadbddcf9fe8bf4fb3a6261effba3", size = 11161, upload-time = "2025-06-14T20:56:32.819Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a9/32/ad08b45fc0ad9850054ffe66fb0cb2ff7af3d2007c192dda14cf9a3ea893/pyobjc-11.1-py3-none-any.whl", hash = "sha256:903f822cba40be53d408b8eaf834514937ec0b4e6af1c5ecc24fcb652812dd85", size = 4164, upload-time = "2025-06-14T20:44:42.659Z" },
]

[[package]]
name = "pyobjc-core"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e8/e9/0b85c81e2b441267bca707b5d89f56c2f02578ef8f3eafddf0e0c0b8848c/pyobjc_core-11.1.tar.gz", hash = "sha256:b63d4d90c5df7e762f34739b39cc55bc63dbcf9fb2fb3f2671e528488c7a87fe", size = 974602, upload-time = "2025-06-14T20:56:34.189Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c0/09/e83228e878e73bf756749939f906a872da54488f18d75658afa7f1abbab1/pyobjc_core-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:765b97dea6b87ec4612b3212258024d8496ea23517c95a1c5f0735f96b7fd529", size = 677985, upload-time = "2025-06-14T20:44:48.375Z" },
    { url = "https://files.pythonhosted.org/packages/c5/24/12e4e2dae5f85fd0c0b696404ed3374ea6ca398e7db886d4f1322eb30799/pyobjc_core-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:18986f83998fbd5d3f56d8a8428b2f3e0754fd15cef3ef786ca0d29619024f2c", size = 676431, upload-time = "2025-06-14T20:44:49.908Z" },
    { url = "https://files.pythonhosted.org/packages/f7/79/031492497624de4c728f1857181b06ce8c56444db4d49418fa459cba217c/pyobjc_core-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8849e78cfe6595c4911fbba29683decfb0bf57a350aed8a43316976ba6f659d2", size = 719330, upload-time = "2025-06-14T20:44:51.621Z" },
    { url = "https://files.pythonhosted.org/packages/ed/7d/6169f16a0c7ec15b9381f8bf33872baf912de2ef68d96c798ca4c6ee641f/pyobjc_core-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:8cb9ed17a8d84a312a6e8b665dd22393d48336ea1d8277e7ad20c19a38edf731", size = 667203, upload-time = "2025-06-14T20:44:53.262Z" },
    { url = "https://files.pythonhosted.org/packages/49/0f/f5ab2b0e57430a3bec9a62b6153c0e79c05a30d77b564efdb9f9446eeac5/pyobjc_core-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f2455683e807f8541f0d83fbba0f5d9a46128ab0d5cc83ea208f0bec759b7f96", size = 708807, upload-time = "2025-06-14T20:44:54.851Z" },
]

[[package]]
name = "pyobjc-framework-accessibility"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/78/b4/10c16e9d48568a68da2f61866b19468d4ac7129c377d4b1333ee936ae5d0/pyobjc_framework_accessibility-11.1.tar.gz", hash = "sha256:c0fa5f1e00906ec002f582c7d3d80463a46d19f672bf5ec51144f819eeb40656", size = 45098, upload-time = "2025-06-14T20:56:35.287Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5d/bd/087d511e0ea356434399609a38e8819978943cbeaca3ca7cc5f35c93d0b2/pyobjc_framework_accessibility-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a049b63b32514da68aaaeef0d6c00a125e0618e4042aa6dbe3867b74fb2a8b2b", size = 11158, upload-time = "2025-06-14T20:44:59.032Z" },
    { url = "https://files.pythonhosted.org/packages/0e/1e/4095d683954401d5f7926827fd09f4d399a8923e0e66d386a8903c0950e0/pyobjc_framework_accessibility-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fd5a03b731d1a2bbb2bf706b58889a5e82df82ac69210ec3245c7dc69e42a63a", size = 11177, upload-time = "2025-06-14T20:45:00.111Z" },
    { url = "https://files.pythonhosted.org/packages/28/7f/63d88c16e87f07b7bfff2adc7e74dcb2739cc1aed2110d29489514c05afa/pyobjc_framework_accessibility-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3496c55569a421ef3c98ea66fc0ebaf68c686ede5b26db0fdcb0b0ad4191a20b", size = 11356, upload-time = "2025-06-14T20:45:01.183Z" },
    { url = "https://files.pythonhosted.org/packages/ee/bd/7062e8670f7636aed8d61bde807a458a21962585e9d352cd576631a5eb96/pyobjc_framework_accessibility-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7c4124981a5d84b71464babb4babfbeb5bfab145bc75b6f3577bd046a9579226", size = 11246, upload-time = "2025-06-14T20:45:02.21Z" },
    { url = "https://files.pythonhosted.org/packages/73/79/66e1500a49203931d5b18fd4ae2f40139c27063e6724536d803d07b5bc14/pyobjc_framework_accessibility-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ea98239e339136e3d20d753afe7908006cf29567ba39b8e83ceda7c221e6aad1", size = 11438, upload-time = "2025-06-14T20:45:02.923Z" },
]

[[package]]
name = "pyobjc-framework-accounts"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/12/45/ca21003f68ad0f13b5a9ac1761862ad2ddd83224b4314a2f7d03ca437c8d/pyobjc_framework_accounts-11.1.tar.gz", hash = "sha256:384fec156e13ff75253bb094339013f4013464f6dfd47e2f7de3e2ae7441c030", size = 17086, upload-time = "2025-06-14T20:56:36.035Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6d/db/fa1c4a964fb9f390af8fce1d82c053f9d4467ffe6acdaab464bb3220e673/pyobjc_framework_accounts-11.1-py2.py3-none-any.whl", hash = "sha256:9c3fe342be7b8e73cba735e5a38affbe349cf8bc19091aa4fd788eabf2074b72", size = 5117, upload-time = "2025-06-14T20:45:04.696Z" },
]

[[package]]
name = "pyobjc-framework-addressbook"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/eb/d3/f5bb5c72be5c6e52224f43e23e5a44e86d2c35ee9af36939e5514c6c7a0f/pyobjc_framework_addressbook-11.1.tar.gz", hash = "sha256:ce2db3be4a3128bf79d5c41319a6d16b73754785ce75ac694d0d658c690922fc", size = 97609, upload-time = "2025-06-14T20:56:37.324Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/de/e1ba5f113c05b543a097040add795fa4b85fdd5ad850b56d83cd6ce8afff/pyobjc_framework_addressbook-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:fb3d0a710f8342a0c63a8e4caf64a044b4d7e42d6d242c8e1b54470238b938cb", size = 13173, upload-time = "2025-06-14T20:45:07.755Z" },
    { url = "https://files.pythonhosted.org/packages/59/53/a0487a0fbc9134e69e29f18334d0b610c44578d753e8264ea1ac649f2839/pyobjc_framework_addressbook-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:411adf4874cc4343f2928a26fe4cb3673d2f5f73365b45cd3650aa7304a45e24", size = 13188, upload-time = "2025-06-14T20:45:08.811Z" },
    { url = "https://files.pythonhosted.org/packages/81/07/1ca336107358ad526394a720598b8549f613ef1797350c764535f26e47bc/pyobjc_framework_addressbook-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6735f297f0e5fd109fa77ca90cace57eb2e10eb65e3c15ccd249df2228030d3b", size = 13358, upload-time = "2025-06-14T20:45:09.877Z" },
    { url = "https://files.pythonhosted.org/packages/96/f7/c5ca9d90b2f6c6c04df8c61f788c5667467d1c63b8ccb85521eab9d463f7/pyobjc_framework_addressbook-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e4004bdf134a069c58d91b231cbeb9e0adad26a73d2689015baaf6a98c411c54", size = 13228, upload-time = "2025-06-14T20:45:10.601Z" },
    { url = "https://files.pythonhosted.org/packages/6a/14/275315178d6fa10ebc51d9713580ed53b6df3b3773600cfaef6ca4aa9baf/pyobjc_framework_addressbook-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6bc42832e85f418a9f978b7e001e219faf52cbb279a0df185115cd4292c381cb", size = 13396, upload-time = "2025-06-14T20:45:11.822Z" },
]

[[package]]
name = "pyobjc-framework-adservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2a/3f/af76eab6eee0a405a4fdee172e7181773040158476966ecd757b0a98bfc5/pyobjc_framework_adservices-11.1.tar.gz", hash = "sha256:44c72f8163705c9aa41baca938fdb17dde257639e5797e6a5c3a2b2d8afdade9", size = 12473, upload-time = "2025-06-14T20:56:38.147Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8e/11/a63a171ce86c25a6ae85ebff6a9ab92b0d0cb1fd66ddc7d7b0d803f36191/pyobjc_framework_adservices-11.1-py2.py3-none-any.whl", hash = "sha256:1744f59a75b2375e139c39f3e85658e62cd10cc0f12b158a80421f18734e9ffc", size = 3474, upload-time = "2025-06-14T20:45:13.263Z" },
]

[[package]]
name = "pyobjc-framework-adsupport"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7f/03/9c51edd964796a97def4e1433d76a128dd7059b685fb4366081bf4e292ba/pyobjc_framework_adsupport-11.1.tar.gz", hash = "sha256:78b9667c275785df96219d205bd4309731869c3298d0931e32aed83bede29096", size = 12556, upload-time = "2025-06-14T20:56:38.741Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7d/b8/ad895efb24311cab2b9d6f7f7f6a833b7f354f80fec606e6c7893da9349b/pyobjc_framework_adsupport-11.1-py2.py3-none-any.whl", hash = "sha256:c3e009612778948910d3a7135b9d77b9b7c06aab29d40957770834c083acf825", size = 3387, upload-time = "2025-06-14T20:45:14.394Z" },
]

[[package]]
name = "pyobjc-framework-applescriptkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bc/63/1bcfcdca53bf5bba3a7b4d73d24232ae1721a378a32fd4ebc34a35549df2/pyobjc_framework_applescriptkit-11.1.tar.gz", hash = "sha256:477707352eaa6cc4a5f8c593759dc3227a19d5958481b1482f0d59394a4601c3", size = 12392, upload-time = "2025-06-14T20:56:39.331Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c6/0e/68ac4ce71e613697a087c262aefacc9ed54eaf0cf1d9ffcd89134bfdab9b/pyobjc_framework_applescriptkit-11.1-py2.py3-none-any.whl", hash = "sha256:e22cbc9d1a25a4a713f21aa94dd017c311186b02062fc7ffbde3009495fb0067", size = 4334, upload-time = "2025-06-14T20:45:15.205Z" },
]

[[package]]
name = "pyobjc-framework-applescriptobjc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a3/27/687b55b575367df045879b786f358355e40e41f847968e557d0718a6c4a4/pyobjc_framework_applescriptobjc-11.1.tar.gz", hash = "sha256:c8a0ec975b64411a4f16a1280c5ea8dbe949fd361e723edd343102f0f95aba6e", size = 12445, upload-time = "2025-06-14T20:56:39.976Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2d/33/ceb6a512b41fbf3458b9a281997ebb3056cc354981215261f0a2bf7d15d6/pyobjc_framework_applescriptobjc-11.1-py2.py3-none-any.whl", hash = "sha256:ac22526fd1f0a3b07ac1d77f90046b77f10ec9549182114f2428ee1e96d3de2b", size = 4433, upload-time = "2025-06-14T20:45:16.061Z" },
]

[[package]]
name = "pyobjc-framework-applicationservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coretext" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/be/3f/b33ce0cecc3a42f6c289dcbf9ff698b0d9e85f5796db2e9cb5dadccffbb9/pyobjc_framework_applicationservices-11.1.tar.gz", hash = "sha256:03fcd8c0c600db98fa8b85eb7b3bc31491701720c795e3f762b54e865138bbaf", size = 224842, upload-time = "2025-06-14T20:56:40.648Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/38/ec/46a5c710e2d7edf55105223c34fed5a7b7cc7aba7d00a3a7b0405d6a2d1a/pyobjc_framework_applicationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f4a85ccd78bab84f7f05ac65ff9be117839dfc09d48c39edd65c617ed73eb01c", size = 31056, upload-time = "2025-06-14T20:45:18.925Z" },
    { url = "https://files.pythonhosted.org/packages/c4/06/c2a309e6f37bfa73a2a581d3301321b2033e25b249e2a01e417a3c34e799/pyobjc_framework_applicationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:385a89f4d0838c97a331e247519d9e9745aa3f7427169d18570e3c664076a63c", size = 31072, upload-time = "2025-06-14T20:45:19.707Z" },
    { url = "https://files.pythonhosted.org/packages/b4/5f/357bf498c27f1b4d48385860d8374b2569adc1522aabe32befd77089c070/pyobjc_framework_applicationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f480fab20f3005e559c9d06c9a3874a1f1c60dde52c6d28a53ab59b45e79d55f", size = 31335, upload-time = "2025-06-14T20:45:20.462Z" },
    { url = "https://files.pythonhosted.org/packages/ab/b6/797fdd81399fe8251196f29a621ba3f3f04d5c579d95fd304489f5558202/pyobjc_framework_applicationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e8dee91c6a14fd042f98819dc0ac4a182e0e816282565534032f0e544bfab143", size = 31196, upload-time = "2025-06-14T20:45:21.555Z" },
    { url = "https://files.pythonhosted.org/packages/68/45/47eba8d7cdf16d778240ed13fb405e8d712464170ed29d0463363a695194/pyobjc_framework_applicationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a0ce40a57a9b993793b6f72c4fd93f80618ef54a69d76a1da97b8360a2f3ffc5", size = 31446, upload-time = "2025-06-14T20:45:22.313Z" },
]

[[package]]
name = "pyobjc-framework-apptrackingtransparency"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/49/68/7aa3afffd038dd6e5af764336bca734eb910121013ca71030457b61e5b99/pyobjc_framework_apptrackingtransparency-11.1.tar.gz", hash = "sha256:796cc5f83346c10973806cfb535d4200b894a5d2626ff2eeb1972d594d14fed4", size = 13135, upload-time = "2025-06-14T20:56:41.494Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/21/37/22cc0293c911a98a49c5fc007b968d82797101dd06e89c4c3266564ff443/pyobjc_framework_apptrackingtransparency-11.1-py2.py3-none-any.whl", hash = "sha256:e25c3eae25d24ee8b523b7ecc4d2b07af37c7733444b80c4964071dea7b0cb19", size = 3862, upload-time = "2025-06-14T20:45:23.851Z" },
]

[[package]]
name = "pyobjc-framework-audiovideobridging"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c3/25/6c5a7b1443d30139cc722029880284ea9dfa575f0436471b9364fcd499f5/pyobjc_framework_audiovideobridging-11.1.tar.gz", hash = "sha256:12756b3aa35083b8ad5c9139b6a0e2f4792e217096b5bf6b702d499038203991", size = 72913, upload-time = "2025-06-14T20:56:42.128Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/69/3e8e3da4db835168d18155a2c90fcca441047fc9c2e021d2ea01b4c6eb8c/pyobjc_framework_audiovideobridging-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:591e80ff6973ea51a12f7c1a2e3fd59496633a51d5a1bf73f4fb989a43e23681", size = 11032, upload-time = "2025-06-14T20:45:26.196Z" },
    { url = "https://files.pythonhosted.org/packages/0b/93/cf38f503f378e224a57f99f8ca7f044f2690221dc8deaf49b305a6ee439a/pyobjc_framework_audiovideobridging-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:30a12be3784f41e1c6b5ef532c08e73bae7071d9a036b26b1e36b919ee5b6f57", size = 11043, upload-time = "2025-06-14T20:45:27.214Z" },
    { url = "https://files.pythonhosted.org/packages/cf/ed/b2804e0415429292fd2f891f29e57b5008a2ecebb7de83aa9b78281e9284/pyobjc_framework_audiovideobridging-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3bef4383dc9233dbd9efc3817ce9c8fe8670c61d21a94de3c149e7f460245792", size = 11217, upload-time = "2025-06-14T20:45:27.892Z" },
    { url = "https://files.pythonhosted.org/packages/a4/34/6a92d1795bf246222a6e3c993ae12f95b3453c1777ee564ef685b7c31260/pyobjc_framework_audiovideobridging-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:6159b94448af08c9b119eb6ecf3fdbc2b3348ad66fb99586f991939779e412ec", size = 11075, upload-time = "2025-06-14T20:45:28.939Z" },
    { url = "https://files.pythonhosted.org/packages/33/7d/975b7d24b103e015f2289cc160ea01b47b43a242b6f69f0b23a19e38b8bc/pyobjc_framework_audiovideobridging-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e466561bd9eb77be050aabead6ad7313a480d05389d9892e1db2cbc06ce1f475", size = 11248, upload-time = "2025-06-14T20:45:29.959Z" },
]

[[package]]
name = "pyobjc-framework-authenticationservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8f/b7/3e9ad0ed3625dc02e495615ea5dbf55ca95cbd25b3e31f25092f5caad640/pyobjc_framework_authenticationservices-11.1.tar.gz", hash = "sha256:8fd801cdb53d426b4e678b0a8529c005d0c44f5a17ccd7052a7c3a1a87caed6a", size = 115266, upload-time = "2025-06-14T20:56:42.889Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7e/2d/cbb5e88c3713fb68cda7d76d37737076c1653bf1ac95418c30d4b614f4be/pyobjc_framework_authenticationservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6655dd53d9135ef85265a4297da5e7459ed7836973f2796027fdfbfd7f08e433", size = 20385, upload-time = "2025-06-14T20:45:33.359Z" },
    { url = "https://files.pythonhosted.org/packages/53/ac/cfd8aed9fba6974f291b3beb198c7270e4a3cae9f1ff9600bd0e4c904ae9/pyobjc_framework_authenticationservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:364035d265129192e6906f7a94cbdf714d737b6b9f20e56bfe74d0007c8761b1", size = 20401, upload-time = "2025-06-14T20:45:34.114Z" },
    { url = "https://files.pythonhosted.org/packages/58/37/949c2f06ea52d976ff7c2c52a58504456ae4cc4f6c681e65ea9fa448a676/pyobjc_framework_authenticationservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e92bf7e829229fbecba4f7f649d3ae38760cf25aa9e909c0e737b1945f36b62d", size = 20636, upload-time = "2025-06-14T20:45:34.875Z" },
    { url = "https://files.pythonhosted.org/packages/15/75/6372808569c763ea00ba393d4eaee5cf4f73fd4fd5b222042e1c0d2aac65/pyobjc_framework_authenticationservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:60bf585e561d885cc88a21713ef2db259baf6434ce7116f82265a0c727f29dba", size = 20574, upload-time = "2025-06-14T20:45:35.947Z" },
    { url = "https://files.pythonhosted.org/packages/74/25/996581a175ce0394ee1abb76c4798478bc0ef32f55a78d4b49079b24fd78/pyobjc_framework_authenticationservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f19ea757ecfda6ac929559c779c3afb001855dd5e41e4acc4c42343c7d912da6", size = 20822, upload-time = "2025-06-14T20:45:36.702Z" },
]

[[package]]
name = "pyobjc-framework-automaticassessmentconfiguration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3d/39/d4c94e0245d290b83919854c4f205851cc0b2603f843448fdfb8e74aad71/pyobjc_framework_automaticassessmentconfiguration-11.1.tar.gz", hash = "sha256:70eadbf8600101901a56fcd7014d8941604e14f3b3728bc4fb0178a9a9420032", size = 24933, upload-time = "2025-06-14T20:56:43.984Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5e/e0/5a67f8ee0393447ca8251cbd06788cb7f3a1f4b9b052afd2e1b2cdfcb504/pyobjc_framework_automaticassessmentconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:55d1684dd676730fb1afbc7c67e0669e3a7159f18c126fea7453fe6182c098f9", size = 9193, upload-time = "2025-06-14T20:45:40.52Z" },
    { url = "https://files.pythonhosted.org/packages/58/04/e2fb203d36b7ec96b06ef26cb44b833d64195435bc5d879987238111b524/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fbcbe406c2a02d632885f6b23285c259b715f019b938d666cc554a66ecf5f9c3", size = 9199, upload-time = "2025-06-14T20:45:41.742Z" },
    { url = "https://files.pythonhosted.org/packages/03/d7/bd947463be8b6f1512a99cb605a57a52f960bb70da060e21a23131a55386/pyobjc_framework_automaticassessmentconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e5fa297c7d4db225f75e5d11121fa68e0956c104e14b24250a52157a180e5f6c", size = 9359, upload-time = "2025-06-14T20:45:42.444Z" },
    { url = "https://files.pythonhosted.org/packages/bf/72/b4674dc09acc106be130737b0d18f17ba0b5b72728d52bc951511d4067c0/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b11c33fb6f6092b9e1fb63747f2402f516b7ff0f815be4ece4625f2a2ec954f", size = 9262, upload-time = "2025-06-14T20:45:43.14Z" },
    { url = "https://files.pythonhosted.org/packages/c7/09/05c9cd16cf2374c38c6dbc3b43e84de5fa7435e557985f4403ac7dea33fd/pyobjc_framework_automaticassessmentconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:378d233879bb011ed9d0bcf1b0e3c048fb756023d0f6819e997f62acc2c32bc3", size = 9397, upload-time = "2025-06-14T20:45:43.834Z" },
]

[[package]]
name = "pyobjc-framework-automator"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/63/9f/097ed9f4de9e9491a1b08bb7d85d35a95d726c9e9f5f5bf203b359a436b6/pyobjc_framework_automator-11.1.tar.gz", hash = "sha256:9b46c55a4f9ae2b3c39ff560f42ced66bdd18c093188f0b5fc4060ad911838e4", size = 201439, upload-time = "2025-06-14T20:56:44.767Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0e/1e/3ed1df2168e596151da2329258951dae334e194d7de3b117c7e29a768ffc/pyobjc_framework_automator-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:af5941f8d90167244209b352512b7779e5590d17dc1e703e087a6cfe79ee3d64", size = 10029, upload-time = "2025-06-14T20:45:46.823Z" },
    { url = "https://files.pythonhosted.org/packages/25/ed/a92cea530aac0cf08287321ec8123e8447f93461521f46bb329058b322eb/pyobjc_framework_automator-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3458f836671ea922ad0771f617c927e9c52841c0a6e71b4a5a9dbb438736c207", size = 10040, upload-time = "2025-06-14T20:45:47.549Z" },
    { url = "https://files.pythonhosted.org/packages/e9/30/c284723dd871e59756d24ddb4a9728db87b9e1b1610d22f3f60ad9de8b45/pyobjc_framework_automator-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:203b888152a78b39a8c67be663ff78a749ebff208ce993b4419fc4409faa1fda", size = 10186, upload-time = "2025-06-14T20:45:48.265Z" },
    { url = "https://files.pythonhosted.org/packages/89/ac/a1e4e318bb972c2e62bdd215490bc4c24cdfac881e3ade5660d2b1412779/pyobjc_framework_automator-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:651760236cb2d2481faa5afb66da97054850d34fdbebc5e4ee2f83a683a8be10", size = 10086, upload-time = "2025-06-14T20:45:49.294Z" },
    { url = "https://files.pythonhosted.org/packages/7b/9c/ffcc59f5ff3aadfba6b94ba641c668bca10e0612f8754c25753f0a12f41a/pyobjc_framework_automator-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:112815d2e1b6002b4f9bc644bdae6b02257d249145c79346d7b8bb11e6f76b03", size = 10239, upload-time = "2025-06-14T20:45:50.018Z" },
]

[[package]]
name = "pyobjc-framework-avfoundation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3c/1f/90cdbce1d3b4861cbb17c12adf57daeec32477eb1df8d3f9ab8551bdadfb/pyobjc_framework_avfoundation-11.1.tar.gz", hash = "sha256:6663056cc6ca49af8de6d36a7fff498f51e1a9a7f1bde7afba718a8ceaaa7377", size = 832178, upload-time = "2025-06-14T20:56:46.329Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/43/30/d5d03dd4a508bdaa2156ff379e9e109020de23cbb6316c5865d341aa6db1/pyobjc_framework_avfoundation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:94f065db4e87b1baebb5cf9f464cf9d82c5f903fff192001ebc974d9e3132c7e", size = 70746, upload-time = "2025-06-14T20:45:53.253Z" },
    { url = "https://files.pythonhosted.org/packages/3f/8c/b8ced7700b0e931dc37d14b05e2bead28d2598c887832b3d697da55b1845/pyobjc_framework_avfoundation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:e204d155a09c186601490e4402dcffb2845a5831079e389b47bd6a341fe5ee63", size = 70773, upload-time = "2025-06-14T20:45:54.059Z" },
    { url = "https://files.pythonhosted.org/packages/d6/4c/086f4713793aaabdb5134debbf1fdc6c7d4ef5a32a6b35529e2e69580ec8/pyobjc_framework_avfoundation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dd3965aad0b236b8ac12f216d688c1a22b963f63e7e4fdb7107dd6790e80ee12", size = 71352, upload-time = "2025-06-14T20:45:54.871Z" },
    { url = "https://files.pythonhosted.org/packages/a6/5f/d5c4b9812e22c6fdf234421f131efae7c3137e838bb9df9be8bb45cde97b/pyobjc_framework_avfoundation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1ab2108b652496b13b9758c295f0f6de53b6d12125cf574ddae84ce28044bce1", size = 71208, upload-time = "2025-06-14T20:45:56.057Z" },
    { url = "https://files.pythonhosted.org/packages/29/d0/dec23e1745a81f5576cba577fa7218d665f36250a8507eaaa83a84579abf/pyobjc_framework_avfoundation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5dd6ac6a57f86b7ed5ac0a965ce54328f6ce77816b4a1fbf0d85c06fb251867a", size = 71680, upload-time = "2025-06-14T20:45:57.091Z" },
]

[[package]]
name = "pyobjc-framework-avkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/61/ff/9f41f2b8de786871184b48c4e5052cb7c9fcc204e7fee06687fa32b08bed/pyobjc_framework_avkit-11.1.tar.gz", hash = "sha256:d948204a7b94e0e878b19a909f9b33342e19d9ea519571d66a21fce8f72e3263", size = 46825, upload-time = "2025-06-14T20:56:47.494Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b2/2f/6ec6a4ec7eb9ca329f36bbd2a51750fe5064d44dd437d8615abb7121ec93/pyobjc_framework_avkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ef9cd9fe37c6199bfde7ee5cd6e76ede23a6797932882785c53ef3070e209afb", size = 11539, upload-time = "2025-06-14T20:46:00.375Z" },
    { url = "https://files.pythonhosted.org/packages/16/c8/6f0131f62f70e201a605b762cc05804b01fd493a7f21824d714140b7fd99/pyobjc_framework_avkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c5810b349745078ef8b4a562e85afe40de3245127f633d8cabe98aeca765c7fc", size = 11551, upload-time = "2025-06-14T20:46:01.071Z" },
    { url = "https://files.pythonhosted.org/packages/a9/e6/a5bfa072393416c940a35b182457fee4779cf2f010c5772a9b690522afef/pyobjc_framework_avkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:023b1cdb78c3aa5873d8abe69697396872b47278208991ec5e5aea4464309b01", size = 11749, upload-time = "2025-06-14T20:46:01.785Z" },
    { url = "https://files.pythonhosted.org/packages/35/15/fdb3c2dbce6cc7236bced3874fe5cf4b32b3af786447aae033bb1831f5e9/pyobjc_framework_avkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a6b418603fc270a8e63c2a5efffa753704fd14bf8bca0657901c49a7cc9b22b5", size = 11587, upload-time = "2025-06-14T20:46:02.6Z" },
    { url = "https://files.pythonhosted.org/packages/fc/2e/a311d27ac6785bfe51e6276ad326be90ca928cb07d73fc4fb8e8857f7ce0/pyobjc_framework_avkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3a5f22bc4f4b0b82c8039d37996882bf4a38f509963d1afa3275a45ddd4a0b00", size = 11766, upload-time = "2025-06-14T20:46:03.29Z" },
]

[[package]]
name = "pyobjc-framework-avrouting"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/cf/42/94bc18b968a4ee8b6427257f907ffbfc97f8ba6a6202953da149b649d638/pyobjc_framework_avrouting-11.1.tar.gz", hash = "sha256:7db1291d9f53cc58d34b2a826feb721a85f50ceb5e71952e8762baacd3db3fc0", size = 21069, upload-time = "2025-06-14T20:56:48.57Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/01/17/ce199bc7fb3ba1f7b0474554bd71d1bdd3d5a141e1d9722ff9f46c104e1d/pyobjc_framework_avrouting-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc309e175abf3961f933f8b341c0504b17f4717931242ebb121a83256b8b5c13", size = 8212, upload-time = "2025-06-14T20:46:06.17Z" },
    { url = "https://files.pythonhosted.org/packages/72/39/5c550da37c6d5a18a9b4a7d0fd6f7396ca8fbbee8cfccf82f3298e0f86b3/pyobjc_framework_avrouting-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f52f9d62a3c8485b5687187ea58d905d7edccac9941c444b4add8129841cd031", size = 8230, upload-time = "2025-06-14T20:46:06.919Z" },
    { url = "https://files.pythonhosted.org/packages/6b/ee/fec9662a0f7756a3440cd1c31be8c3a2db98d9b88210e46ca76b36e151ca/pyobjc_framework_avrouting-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6a7b335161d327792f42054acb3ff415f7778e1492582df8e91b8609b4b02244", size = 8383, upload-time = "2025-06-14T20:46:07.593Z" },
    { url = "https://files.pythonhosted.org/packages/41/34/31b10439741980c9f226623ec9cee9649a8ac34a81efd1ad26f72a7d02da/pyobjc_framework_avrouting-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:120c9d65d4f9047b9921f8dced0b4f26d799156bc08ff7e3974217cd036b1bfc", size = 8269, upload-time = "2025-06-14T20:46:08.284Z" },
    { url = "https://files.pythonhosted.org/packages/1d/7b/9fed48dcc1b94fa20d5435c352bea2ce431541e43b43fb720dcb43fc3d16/pyobjc_framework_avrouting-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9aa9b0a7ae7ee5874e7d92bebefca4525d5cf1f0aa1f50e78e558984a39cad2e", size = 8410, upload-time = "2025-06-14T20:46:09.321Z" },
]

[[package]]
name = "pyobjc-framework-backgroundassets"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/08/76/21e1632a212f997d7a5f26d53eb997951978916858039b79f43ebe3d10b2/pyobjc_framework_backgroundassets-11.1.tar.gz", hash = "sha256:2e14b50539d96d5fca70c49f21b69fdbad81a22549e3630f5e4f20d5c0204fc2", size = 24803, upload-time = "2025-06-14T20:56:49.566Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ad/77/a6ad2df35fd71b3c26f52698d25174899ba1be134766022f5bf804ebf12d/pyobjc_framework_backgroundassets-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:13bf451c59b409b6ce1ac0e717a970a1b03bca7a944a7f19219da0d46ab7c561", size = 9707, upload-time = "2025-06-14T20:46:12.88Z" },
    { url = "https://files.pythonhosted.org/packages/1d/7f/ed035866ab6c0573c445a9ed1ceb0912119866c130df7684a2332642520e/pyobjc_framework_backgroundassets-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708466d847a479e1798f31c59fbc5307473d03fa1083f40cfcaa18fd31819c40", size = 9722, upload-time = "2025-06-14T20:46:13.574Z" },
    { url = "https://files.pythonhosted.org/packages/05/e9/15f540b4bee160fd4b66f294ee4cd326aaa94632bcbee12d4b2448bb74ee/pyobjc_framework_backgroundassets-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2484a2f9c87e8cae2fc375a39d68ea7ff02e4fb786e4afe88237c51fd5e78ec9", size = 9899, upload-time = "2025-06-14T20:46:14.277Z" },
    { url = "https://files.pythonhosted.org/packages/9b/aa/17dd9b9def7d9d29c1ee14e1b3100e0bf9dbc5fdd4a12d1bd4c6e79b46d2/pyobjc_framework_backgroundassets-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a72536ed18cf2462085bbb2184d0a3eecf9b97669c0ef4db45418555a609b534", size = 9774, upload-time = "2025-06-14T20:46:14.957Z" },
    { url = "https://files.pythonhosted.org/packages/5a/de/852cb10bb11a0e88d2422f24c2bdb8eeeabf9c0a400e1cba03a7af351dca/pyobjc_framework_backgroundassets-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a4db45048d1021900be5b03136b927773820bcbb40d623aeac54712e1c86d6f6", size = 9948, upload-time = "2025-06-14T20:46:15.655Z" },
]

[[package]]
name = "pyobjc-framework-browserenginekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/30/75/087270d9f81e913b57c7db58eaff8691fa0574b11faf9302340b3b8320f1/pyobjc_framework_browserenginekit-11.1.tar.gz", hash = "sha256:918440cefb10480024f645169de3733e30ede65e41267fa12c7b90c264a0a479", size = 31944, upload-time = "2025-06-14T20:56:50.195Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/90/a50bb66a5e041ace99b6c8b1df43b38d5f2e1bf771f57409e4aebf1dfae5/pyobjc_framework_browserenginekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9b815b167533015d62832b956e9cfb962bd2026f5a4ccd66718cf3bb2e15ab27", size = 11115, upload-time = "2025-06-14T20:46:19.401Z" },
    { url = "https://files.pythonhosted.org/packages/44/0a/3cbfc8ca58ed9aeef7498f318ad209164903e64eba1ea94a661a59ee67e6/pyobjc_framework_browserenginekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:dfe469f8eb1313ea0cbe0616cd3bbc56f62bdd8a683c959819ef01d7e9ac0de7", size = 11134, upload-time = "2025-06-14T20:46:20.445Z" },
    { url = "https://files.pythonhosted.org/packages/4d/d6/013d10fc2ad2c7095e1b61b1b3db2c38aec403784f81b70237d11ba615a8/pyobjc_framework_browserenginekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f3332ffa9ae74cc6633fd17f6d998ac77b8939abbe9ecf95ae56df200ee93853", size = 11322, upload-time = "2025-06-14T20:46:21.476Z" },
    { url = "https://files.pythonhosted.org/packages/63/ba/59869b4f500a1f7edf6eb84b6e018df37655b0b6b96fc6e2d00dfa3b648d/pyobjc_framework_browserenginekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3195c4fb3b84150fac6dd18ce318eaae17f246f98678825397ed80d6da3c371", size = 11170, upload-time = "2025-06-14T20:46:22.52Z" },
    { url = "https://files.pythonhosted.org/packages/c2/9a/0e75c06c0f48c368b7eb2d5aa6bde780106fad080fd74a76e109eef6afc6/pyobjc_framework_browserenginekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1f4cce594a94d0bc0a020122153f8149c16578fa4761b0e27d868c013f76214c", size = 11369, upload-time = "2025-06-14T20:46:23.235Z" },
]

[[package]]
name = "pyobjc-framework-businesschat"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/85/be/9d9d9d9383c411a58323ea510d768443287ca21610af652b815b3205ea80/pyobjc_framework_businesschat-11.1.tar.gz", hash = "sha256:69589d2f0cb4e7892e5ecc6aed79b1abd1ec55c099a7faacae6a326bc921259d", size = 12698, upload-time = "2025-06-14T20:56:51.173Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/87/a4/5b8bb268b263678c0908cdaa8bed2534a6caac5862d05236f6c361d130ba/pyobjc_framework_businesschat-11.1-py2.py3-none-any.whl", hash = "sha256:7fdc1219b988ce3ae896bffd01f547c06cec3b4e4b2d0aa04d251444d7f1c2db", size = 3458, upload-time = "2025-06-14T20:46:24.651Z" },
]

[[package]]
name = "pyobjc-framework-calendarstore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/41/df/7ca8ee65b16d5fc862d7e8664289472eed918cf4d76921de6bdaa1461c65/pyobjc_framework_calendarstore-11.1.tar.gz", hash = "sha256:858ee00e6a380d9c086c2d7db82c116a6c406234038e0ec8fc2ad02e385dc437", size = 68215, upload-time = "2025-06-14T20:56:51.799Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/94/69cb863bd88349df0f6cf491fd3ca4d674816c4d66270f9e2620cc6e16ed/pyobjc_framework_calendarstore-11.1-py2.py3-none-any.whl", hash = "sha256:bf066e17392c978becf17a61863eb81727bf593a2bfdab261177126072557e24", size = 5265, upload-time = "2025-06-14T20:46:25.457Z" },
]

[[package]]
name = "pyobjc-framework-callkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/51/d5/4f0b62ab35be619e8c8d96538a03cf56fde6fd53540e1837e0fa588b3f6c/pyobjc_framework_callkit-11.1.tar.gz", hash = "sha256:b84d5ea38dff0cbe0754f5f9f6f33c742e216f12e7166179a8ec2cf4b0bfca94", size = 46648, upload-time = "2025-06-14T20:56:52.579Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/2a/209572a6dba6768a57667e1f87a83ce8cadf18de5d6b1a91b95ce548d0f8/pyobjc_framework_callkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:554e09ca3dab44d93a89927d9e300f004d2ef0db020b10425a4622b432e7b684", size = 11269, upload-time = "2025-06-14T20:46:28.164Z" },
    { url = "https://files.pythonhosted.org/packages/8f/74/b0a22adb7ebcd0b81c24ed6e49d3df3b84f73192b667ebd90cb1b6eba917/pyobjc_framework_callkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fc5e638ddbc9dd3e9993205d2b077f5db41b6cd4e97b9c5592b7249575f23f04", size = 11284, upload-time = "2025-06-14T20:46:29.197Z" },
    { url = "https://files.pythonhosted.org/packages/a2/98/3f65e4853a4a45b0cf369e5bbb0d9efaad93589461d155119feb88e8ff7b/pyobjc_framework_callkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc1d2349dab93f7a0d298b01893828d7f46aded9122a341469b835d977a0646d", size = 11494, upload-time = "2025-06-14T20:46:30.09Z" },
    { url = "https://files.pythonhosted.org/packages/e4/95/d89e97351570fcfaae843dea29aa06c2a3ff00a6ea8ea4c3e68478620afa/pyobjc_framework_callkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b69b4262897f2701348ea0da36afe32d60f84e2a036baf13e258a97875b25a6c", size = 11305, upload-time = "2025-06-14T20:46:31.099Z" },
    { url = "https://files.pythonhosted.org/packages/2f/38/939b73759cfd1bf6367290c31bfe576fafdd7a351aa867c7c29eba962d1e/pyobjc_framework_callkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8266ee797fdabb657f7cb4fa808404fc33fcf3f31d4bcab1ab3c53d272e1ff83", size = 11504, upload-time = "2025-06-14T20:46:31.784Z" },
]

[[package]]
name = "pyobjc-framework-carbon"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/39/a4/d751851865d9a78405cfec0c8b2931b1e96b9914e9788cd441fa4e8290d0/pyobjc_framework_carbon-11.1.tar.gz", hash = "sha256:047f098535479efa3ab89da1ebdf3cf9ec0b439a33a4f32806193886e9fcea71", size = 37291, upload-time = "2025-06-14T20:56:53.642Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/84/44/f1a20b5aa3833af4d461074c479263a410ef90d17dbec11f78ad9c34dbab/pyobjc_framework_carbon-11.1-py2.py3-none-any.whl", hash = "sha256:1bf66853e939315ad7ee968170b16dd12cb838c42b80dfcd5354687760998825", size = 4753, upload-time = "2025-06-14T20:46:33.141Z" },
]

[[package]]
name = "pyobjc-framework-cfnetwork"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6f/49/7b24172e3d6eb0ddffc33a7498a2bea264aa2958c3fecaeb463bef88f0b8/pyobjc_framework_cfnetwork-11.1.tar.gz", hash = "sha256:ad600163eeadb7bf71abc51a9b6f2b5462a018d3f9bb1510c5ce3fdf2f22959d", size = 79069, upload-time = "2025-06-14T20:56:54.615Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/31/05b4fb79e7f738f7f7d7a58734de2fab47d9a1fb219c2180e8c07efe2550/pyobjc_framework_cfnetwork-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:70beb8095df76e0e8eb7ab218be1e69ae180e01a4d77f7cad73c97b4eb7a296a", size = 19141, upload-time = "2025-06-14T20:46:36.134Z" },
    { url = "https://files.pythonhosted.org/packages/2d/b1/5ea76ffd6413be8c65ec02e4552e3da3ee2bd37449e0854e3c8c559e7e42/pyobjc_framework_cfnetwork-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5dd866fcbe6870931373636d19144544344f0f89685f6720e4a45453957702dd", size = 19148, upload-time = "2025-06-14T20:46:36.876Z" },
    { url = "https://files.pythonhosted.org/packages/ba/df/b4897033b0368e4b6c4e5f643c593801677b2590d48dcb93d1c5a1d66c0f/pyobjc_framework_cfnetwork-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62ccc6dcaaa5877534d21f93a15861a3d8af95888123d659f9ff5383d1a2a1f4", size = 19406, upload-time = "2025-06-14T20:46:37.648Z" },
    { url = "https://files.pythonhosted.org/packages/25/9b/f277fb7a7da804a2b53b2f3dacf1f0196e63536580023bd5377344e1407a/pyobjc_framework_cfnetwork-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4b998daa3e6ce253c48455365f004647b3b1da2f313fbc8a5a607e460b4d5567", size = 19186, upload-time = "2025-06-14T20:46:38.398Z" },
    { url = "https://files.pythonhosted.org/packages/e2/f6/80b5c7bb8247c2bb17c3869389a591f480ef771073c4642fbe49e65f1614/pyobjc_framework_cfnetwork-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e9a4ce6b416bff881df499d9060c1096220ef8c20e519108a7b91692d1fd1d7", size = 19407, upload-time = "2025-06-14T20:46:39.143Z" },
]

[[package]]
name = "pyobjc-framework-cinematic"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/57/6f/c2d0b49e01e654496a1781bafb9da72a6fbd00f5abb39dc4a3a0045167c7/pyobjc_framework_cinematic-11.1.tar.gz", hash = "sha256:efde39a6a2379e1738dbc5434b2470cd187cf3114ffb81390b3b1abda470b382", size = 25522, upload-time = "2025-06-14T20:56:55.379Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/05/bd/a9b51c770bd96546a101c9e9994f851b87336f168a77048241517ca4db8c/pyobjc_framework_cinematic-11.1-py2.py3-none-any.whl", hash = "sha256:b62c024c1a9c7890481bc2fdfaf0cd3c251a4a08357d57dc1795d98920fcdbd1", size = 4562, upload-time = "2025-06-14T20:46:40.989Z" },
]

[[package]]
name = "pyobjc-framework-classkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7a/8b/5150b4faddd15d5dd795bc62b2256c4f7dafc983cfa694fcf88121ea0016/pyobjc_framework_classkit-11.1.tar.gz", hash = "sha256:ee1e26395eb00b3ed5442e3234cdbfe925d2413185af38eca0477d7166651df4", size = 39831, upload-time = "2025-06-14T20:56:56.036Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/75/79/2552fd5e1da73dffb35589469b3cd8c0928e3100462761350d19ea922e59/pyobjc_framework_classkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:161dcb9b718649e6331a5eab5a76c2b43a9b322b15b37b3f8f9c5faad12ee6d1", size = 8911, upload-time = "2025-06-14T20:46:43.714Z" },
    { url = "https://files.pythonhosted.org/packages/59/1c/a06623c3d78949c9d5eae7c7e753e6c8c75e2ae7a0b8ccae40a1b6180e0a/pyobjc_framework_classkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:08000deb43004d16fb39ccd83b3de30e1e3b72639a79d05206d7d5c15f005b3a", size = 8928, upload-time = "2025-06-14T20:46:44.426Z" },
    { url = "https://files.pythonhosted.org/packages/b3/c3/e0a966134c8022f1d922b27fea6a50ec1118c12fdfa65b2ce4efaa7c84d6/pyobjc_framework_classkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ef28d042964b0f757569e72df737bb049b531c33b7d06a705ce2dcfa4e6e45d8", size = 9082, upload-time = "2025-06-14T20:46:45.309Z" },
    { url = "https://files.pythonhosted.org/packages/c7/66/d5113269ee84bebc03576c53394e2b59c25da01f932f2e1cdfc5bd05a5a1/pyobjc_framework_classkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:be279d91f10d68ad9a256e96d26d8975e35b9b1bb304c82491766d29ad252b0d", size = 8958, upload-time = "2025-06-14T20:46:46.329Z" },
    { url = "https://files.pythonhosted.org/packages/ad/72/fff0a96bd7fd9a83ee074330070ebe4a53d99a3c0620c786bb59c04c4a7c/pyobjc_framework_classkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9a1b9d31f9b23e05b92769bbdb4ef2167a59b3b24aefa6af86448f5087a2e105", size = 9120, upload-time = "2025-06-14T20:46:47.015Z" },
]

[[package]]
name = "pyobjc-framework-cloudkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-accounts" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
    { name = "pyobjc-framework-corelocation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/58/a6/bfe5be55ed95704efca0e86b218155a9c801735107cedba3af8ea4580a05/pyobjc_framework_cloudkit-11.1.tar.gz", hash = "sha256:40d2dc4bf28c5be9b836b01e4d267a15d847d756c2a65530e1fcd79b2825e86d", size = 122778, upload-time = "2025-06-14T20:56:56.73Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/25/d9/5570a217cef8130708e860b86f4f22bb5827247c97121523a9dfd4784148/pyobjc_framework_cloudkit-11.1-py2.py3-none-any.whl", hash = "sha256:c583e40c710cf85ebe34173d1d2995e832a20127edc8899b2f35b13f98498af1", size = 10870, upload-time = "2025-06-14T20:46:48.781Z" },
]

[[package]]
name = "pyobjc-framework-cocoa"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4b/c5/7a866d24bc026f79239b74d05e2cf3088b03263da66d53d1b4cf5207f5ae/pyobjc_framework_cocoa-11.1.tar.gz", hash = "sha256:87df76b9b73e7ca699a828ff112564b59251bb9bbe72e610e670a4dc9940d038", size = 5565335, upload-time = "2025-06-14T20:56:59.683Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/68/da/41c0f7edc92ead461cced7e67813e27fa17da3c5da428afdb4086c69d7ba/pyobjc_framework_cocoa-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:806de56f06dfba8f301a244cce289d54877c36b4b19818e3b53150eb7c2424d0", size = 388983, upload-time = "2025-06-14T20:46:52.591Z" },
    { url = "https://files.pythonhosted.org/packages/4e/0b/a01477cde2a040f97e226f3e15e5ffd1268fcb6d1d664885a95ba592eca9/pyobjc_framework_cocoa-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:54e93e1d9b0fc41c032582a6f0834befe1d418d73893968f3f450281b11603da", size = 389049, upload-time = "2025-06-14T20:46:53.757Z" },
    { url = "https://files.pythonhosted.org/packages/bc/e6/64cf2661f6ab7c124d0486ec6d1d01a9bb2838a0d2a46006457d8c5e6845/pyobjc_framework_cocoa-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:fd5245ee1997d93e78b72703be1289d75d88ff6490af94462b564892e9266350", size = 393110, upload-time = "2025-06-14T20:46:54.894Z" },
    { url = "https://files.pythonhosted.org/packages/33/87/01e35c5a3c5bbdc93d5925366421e10835fcd7b23347b6c267df1b16d0b3/pyobjc_framework_cocoa-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aede53a1afc5433e1e7d66568cc52acceeb171b0a6005407a42e8e82580b4fc0", size = 392644, upload-time = "2025-06-14T20:46:56.503Z" },
    { url = "https://files.pythonhosted.org/packages/c1/7c/54afe9ffee547c41e1161691e72067a37ed27466ac71c089bfdcd07ca70d/pyobjc_framework_cocoa-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b5de4e1757bb65689d6dc1f8d8717de9ec8587eb0c4831c134f13aba29f9b71", size = 396742, upload-time = "2025-06-14T20:46:57.64Z" },
]

[[package]]
name = "pyobjc-framework-collaboration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/66/49/9dbe8407d5dd663747267c1234d1b914bab66e1878d22f57926261a3063b/pyobjc_framework_collaboration-11.1.tar.gz", hash = "sha256:4564e3931bfc51773623d4f57f2431b58a39b75cb964ae5c48d27ee4dde2f4ea", size = 16839, upload-time = "2025-06-14T20:57:01.101Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/24/4c9deedcc62d223a45d4b4fa16162729923d2b3e2231467de6ecd079f3f8/pyobjc_framework_collaboration-11.1-py2.py3-none-any.whl", hash = "sha256:3629ea5b56c513fb330d43952afabb2df2a2ac2f9048b8ec6e8ab4486191390a", size = 4891, upload-time = "2025-06-14T20:46:59.734Z" },
]

[[package]]
name = "pyobjc-framework-colorsync"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b5/97/7613b6041f62c52f972e42dd5d79476b56b84d017a8b5e4add4d9cfaca36/pyobjc_framework_colorsync-11.1.tar.gz", hash = "sha256:7a346f71f34b2ccd1b020a34c219b85bf8b6f6e05283d503185aeb7767a269dd", size = 38999, upload-time = "2025-06-14T20:57:01.761Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/d5/c8fc7c47cbb9865058094dc9cf3f57879156ff55fb261cf199e7081d1db7/pyobjc_framework_colorsync-11.1-py2.py3-none-any.whl", hash = "sha256:d19d6da2c7175a3896a63c9b40a8ab98ade0779a5b40062789681501c33efd5c", size = 5971, upload-time = "2025-06-14T20:47:00.547Z" },
]

[[package]]
name = "pyobjc-framework-contacts"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a6/85/34868b6447d552adf8674bac226b55c2baacacee0d67ee031e33805d6faa/pyobjc_framework_contacts-11.1.tar.gz", hash = "sha256:752036e7d8952a4122296d7772f274170a5f35a53ee6454a27f3e1d9603222cc", size = 84814, upload-time = "2025-06-14T20:57:02.582Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/c8/0d47af11112bf382e059cfe2dd03be98914f0621ddff8858bb9af864f8c5/pyobjc_framework_contacts-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:576ee4aec05d755444bff10b45833f73083b5b3d1b2740e133b92111f7765e54", size = 12141, upload-time = "2025-06-14T20:47:02.884Z" },
    { url = "https://files.pythonhosted.org/packages/11/af/375aa44e9e00aa66e373c4c3893a0db341d93f90e2d62a277287dc553841/pyobjc_framework_contacts-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:09b873d2bd739fea63d744430defb04ce4b44af064aaf0b6bf558eea23f82bd7", size = 12160, upload-time = "2025-06-14T20:47:03.614Z" },
    { url = "https://files.pythonhosted.org/packages/a0/b9/effeda0eefedced16d4a002ab0c0a331be506d5bc7ff290788ac8eb0b2a9/pyobjc_framework_contacts-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:23312bb4bfc5aafecdac84ca402189e312e754e9dc0586d8f282d225c3952c00", size = 12319, upload-time = "2025-06-14T20:47:04.316Z" },
    { url = "https://files.pythonhosted.org/packages/93/9c/25c6e7ba0fe1d18206decd3e2b47bf110047dda89f7411fe430c0bfd4268/pyobjc_framework_contacts-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3409aba6e23cb179b3fe932c1a0a53d7b273ac8292d5adf1bf6849e925cc0955", size = 12237, upload-time = "2025-06-14T20:47:05.01Z" },
    { url = "https://files.pythonhosted.org/packages/32/fc/0a519a38eada4bf4ed6f502920077e5313fdb1f3eec668438460a797ce47/pyobjc_framework_contacts-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:910f40a2e4d80a97f282bfdecba0f5ff95201b11844acd3f9cb9522db364ab57", size = 12393, upload-time = "2025-06-14T20:47:05.707Z" },
]

[[package]]
name = "pyobjc-framework-contactsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-contacts" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3f/57/8765b54a30edaa2a56df62e11e7c32e41b6ea300513256adffa191689368/pyobjc_framework_contactsui-11.1.tar.gz", hash = "sha256:5bc29ea2b10a342018e1b96be6b140c10ebe3cfb6417278770feef5e88026a1f", size = 20031, upload-time = "2025-06-14T20:57:03.603Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/46/b6/50ec09f1bb18c422b8c079e02328689f32e977b43ab7651c05e8274854dc/pyobjc_framework_contactsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c34a6f27ef5aa4742cc44fd5b4d16fe1e1745ff839578b4c059faf2c58eee3ca", size = 7875, upload-time = "2025-06-14T20:47:09.041Z" },
    { url = "https://files.pythonhosted.org/packages/8b/3f/72170303c11945c360b83fa1c0d3f91638dc5de1ef9f9a2b880252378430/pyobjc_framework_contactsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f3b4f0225645a26ed9e6c008c2e8c217035b4a50fa9cd6623c628a11c37924d0", size = 7886, upload-time = "2025-06-14T20:47:09.726Z" },
    { url = "https://files.pythonhosted.org/packages/ad/d7/fd11ac75bd6eb5d23225f7d1ac910c2b47481caff6e04b883bec04c28de2/pyobjc_framework_contactsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:666586174b306b33b791d2edee021cd979a8c970d444f906ed294e27583a6b54", size = 8044, upload-time = "2025-06-14T20:47:10.427Z" },
    { url = "https://files.pythonhosted.org/packages/05/64/aee816b82564c693fea199178ac791dd384d602b6c772b7f829fb1b8405d/pyobjc_framework_contactsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:7901eed3c669ad52cca86089c443fd30820b21586bf758e03fb83696f435ba87", size = 7937, upload-time = "2025-06-14T20:47:11.182Z" },
    { url = "https://files.pythonhosted.org/packages/34/d4/fe2495ac19d83cc211a639b3654d4ea0f173d053cca387a4448a70d1a1f6/pyobjc_framework_contactsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8b03bd175095b4774c55bd5f38a01942e945b668bea15b9dc3b4f1a28b1a8696", size = 8091, upload-time = "2025-06-14T20:47:11.884Z" },
]

[[package]]
name = "pyobjc-framework-coreaudio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/39/c0/4ab6005cf97e534725b0c14b110d4864b367c282b1c5b0d8f42aad74a83f/pyobjc_framework_coreaudio-11.1.tar.gz", hash = "sha256:b7b89540ae7efc6c1e3208ac838ef2acfc4d2c506dd629d91f6b3b3120e55c1b", size = 141032, upload-time = "2025-06-14T20:57:04.348Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/fe/c43521642db98a4ec29fa535781c1316342bb52d5fc709696cbb1e8ca6cd/pyobjc_framework_coreaudio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2538d1242dab4e27efb346eafbad50594e7e95597fa7220f0bab2099c825da55", size = 36765, upload-time = "2025-06-14T20:47:15.344Z" },
    { url = "https://files.pythonhosted.org/packages/82/9b/24d03ace273585de2d04385f06b895ce92caf8f5af430b060618ebce9dbe/pyobjc_framework_coreaudio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f73d996df1e721931d9f78050e1708735a173dbe3a76d9c71fb36e04f7208478", size = 36779, upload-time = "2025-06-14T20:47:16.123Z" },
    { url = "https://files.pythonhosted.org/packages/91/23/aa78365e45d0d04fc37e21cf7d69dc0d11e17b564e83cb5bcd98e89cdf45/pyobjc_framework_coreaudio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:67dae111b78d91c26c753dbfbccc3ea5498cfda3dfe83c6f3778628b435e1e7b", size = 38480, upload-time = "2025-06-14T20:47:16.911Z" },
    { url = "https://files.pythonhosted.org/packages/3e/58/fc6d752a68f28567fa6d6d6a229122c829e2251f79ec7304fe0572e0fdcd/pyobjc_framework_coreaudio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9527a16a2b88b37bace578d499f21229f9a33b9afdcdd35d4f44374cb8eb9ab6", size = 36910, upload-time = "2025-06-14T20:47:17.69Z" },
    { url = "https://files.pythonhosted.org/packages/9e/4c/c1c5624418dea005d9965ba690d3649afc33371ade213841ab51922af751/pyobjc_framework_coreaudio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6ba8b67f185c0e3f26b17ae525cee3f411bc8d6e9c9a8bfd899a28f594623d2f", size = 38567, upload-time = "2025-06-14T20:47:18.45Z" },
]

[[package]]
name = "pyobjc-framework-coreaudiokit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreaudio" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f1/4e/c49b26c60047c511727efe994b412276c487dfe90f1ee0fced0bddbdf8a3/pyobjc_framework_coreaudiokit-11.1.tar.gz", hash = "sha256:0b461c3d6123fda4da6b6aaa022efc918c1de2e126a5cf07d2189d63fa54ba40", size = 21955, upload-time = "2025-06-14T20:57:05.218Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/27/d8ff6293851a7d9665724fa5c324d28200776ec10a04b850ba21ad1f9be1/pyobjc_framework_coreaudiokit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:20440a2926b1d91da8efc8bc060e77c7a195cb0443dbf3770eaca9e597276748", size = 7266, upload-time = "2025-06-14T20:47:22.136Z" },
    { url = "https://files.pythonhosted.org/packages/13/e6/89aa525271d19f0ea11799021f364181dd62dbfe77ecb4fc0a7d4e579cd2/pyobjc_framework_coreaudiokit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:11d42770dfbc6a8af8d5fa39a4f700f0067d7e6c7ba9335e6624d89de3c599a9", size = 7273, upload-time = "2025-06-14T20:47:23.137Z" },
    { url = "https://files.pythonhosted.org/packages/a5/70/f9b13b7822a53bed794525214ccca63b018901c113ebfd45e2159447f3cf/pyobjc_framework_coreaudiokit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6fea7c7ea5305e8cbd75808ec4edcde8e2320137f227b3d771266dd9a71e1fa5", size = 7429, upload-time = "2025-06-14T20:47:24.17Z" },
    { url = "https://files.pythonhosted.org/packages/a7/d0/aba10b553783c9940b81cb67ad3cae4d4c72e67d4c1af8f4cbe2d9a642d8/pyobjc_framework_coreaudiokit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a71447196a48869b551a2e3b6ba92f39241cb64d0257120505c62ddb611aef0f", size = 7301, upload-time = "2025-06-14T20:47:25.023Z" },
    { url = "https://files.pythonhosted.org/packages/90/9a/a4b7fc47896f1739b8346d21c1b40f536e317f3de416b5cbf12c50445979/pyobjc_framework_coreaudiokit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8d012561eb95877f0214aa0cd13043b1a2693add4a9534d1e6fb82f6d7183c7c", size = 7451, upload-time = "2025-06-14T20:47:26.063Z" },
]

[[package]]
name = "pyobjc-framework-corebluetooth"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3d/fe/2081dfd9413b7b4d719935c33762fbed9cce9dc06430f322d1e2c9dbcd91/pyobjc_framework_corebluetooth-11.1.tar.gz", hash = "sha256:1deba46e3fcaf5e1c314f4bbafb77d9fe49ec248c493ad00d8aff2df212d6190", size = 60337, upload-time = "2025-06-14T20:57:05.919Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/bc/083ea1ae57a31645df7fad59921528f6690995f7b7c84a203399ded7e7fe/pyobjc_framework_corebluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:36bef95a822c68b72f505cf909913affd61a15b56eeaeafea7302d35a82f4f05", size = 13163, upload-time = "2025-06-14T20:47:29.624Z" },
    { url = "https://files.pythonhosted.org/packages/3e/b5/d07cfa229e3fa0cd1cdaa385774c41907941d25b693cf55ad92e8584a3b3/pyobjc_framework_corebluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:992404b03033ecf637e9174caed70cb22fd1be2a98c16faa699217678e62a5c7", size = 13179, upload-time = "2025-06-14T20:47:30.376Z" },
    { url = "https://files.pythonhosted.org/packages/7a/10/476bca43002a6d009aed956d5ed3f3867c8d1dcd085dde8989be7020c495/pyobjc_framework_corebluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ebb8648f5e33d98446eb1d6c4654ba4fcc15d62bfcb47fa3bbd5596f6ecdb37c", size = 13358, upload-time = "2025-06-14T20:47:31.114Z" },
    { url = "https://files.pythonhosted.org/packages/b0/49/6c050dffb9acc49129da54718c545bc5062f61a389ebaa4727bc3ef0b5a9/pyobjc_framework_corebluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e84cbf52006a93d937b90421ada0bc4a146d6d348eb40ae10d5bd2256cc92206", size = 13245, upload-time = "2025-06-14T20:47:31.939Z" },
    { url = "https://files.pythonhosted.org/packages/36/15/9068e8cb108e19e8e86cbf50026bb4c509d85a5d55e2d4c36e292be94337/pyobjc_framework_corebluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4da1106265d7efd3f726bacdf13ba9528cc380fb534b5af38b22a397e6908291", size = 13439, upload-time = "2025-06-14T20:47:32.66Z" },
]

[[package]]
name = "pyobjc-framework-coredata"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/00/e3/af497da7a7c895b6ff529d709d855a783f34afcc4b87ab57a1a2afb3f876/pyobjc_framework_coredata-11.1.tar.gz", hash = "sha256:fe9fd985f8e06c70c0fb1e6bbea5b731461f9e76f8f8d8e89c7c72667cdc6adf", size = 260628, upload-time = "2025-06-14T20:57:06.729Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5b/ac/77935aa9891bd6be952b1e6780df2bae748971dd0fe0b5155894004840bd/pyobjc_framework_coredata-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c9b2374784e67694a18fc8c120a12f11b355a20b643c01f23ae2ce87330a75e0", size = 16443, upload-time = "2025-06-14T20:47:35.711Z" },
    { url = "https://files.pythonhosted.org/packages/75/50/17631c3f172d9681faad210b035fa3d2c01f59468b574dbc088512853cc2/pyobjc_framework_coredata-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:007160eb10bb8c789076f231e3d625d8875ca42eb5a806fdab5d0277c48866f8", size = 16457, upload-time = "2025-06-14T20:47:36.439Z" },
    { url = "https://files.pythonhosted.org/packages/1f/d7/c736d0a945efe806996335324a241f9e2726ebc8a91c9c3cfaa2d788c63b/pyobjc_framework_coredata-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:699ad568f98f58e88e642159c91ffff0c68ce3d1ec798e4af8333b27431fd058", size = 16608, upload-time = "2025-06-14T20:47:37.526Z" },
    { url = "https://files.pythonhosted.org/packages/fa/b9/22c554e3a7d121145aedaab580a88bf35935fc81f693e5071ed8aa7d299e/pyobjc_framework_coredata-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d84afaccbb4f18dbda4c557cd059b7adc2116436a065353e25e7cbc840d9f8b4", size = 16500, upload-time = "2025-06-14T20:47:38.271Z" },
    { url = "https://files.pythonhosted.org/packages/d1/2e/8562252a30644ac5209365358a30cfc53a46609959beaafceffde7381e54/pyobjc_framework_coredata-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:89dde863eff01ed6b5f8d88c764a08b154ef37078397c98c5f403e8798723b9d", size = 16659, upload-time = "2025-06-14T20:47:39.042Z" },
]

[[package]]
name = "pyobjc-framework-corehaptics"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5f/83/cc997ec4687a68214dd3ad1bdf64353305f5c7e827fad211adac4c28b39f/pyobjc_framework_corehaptics-11.1.tar.gz", hash = "sha256:e5da3a97ed6aca9b7268c8c5196c0a339773a50baa72d1502d3435dc1a2a80f1", size = 42722, upload-time = "2025-06-14T20:57:08.019Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/21/d0/0fb20c0f19beae53c905653ffdcbf32e3b4119420c737ff4733f7ebb3b29/pyobjc_framework_corehaptics-11.1-py2.py3-none-any.whl", hash = "sha256:8f8c47ccca5052d07f95d2f35e6e399c5ac1f2072ba9d9eaae902edf4e3a7af4", size = 5363, upload-time = "2025-06-14T20:47:40.582Z" },
]

[[package]]
name = "pyobjc-framework-corelocation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/95/ef/fbd2e01ec137208af7bfefe222773748d27f16f845b0efa950d65e2bd719/pyobjc_framework_corelocation-11.1.tar.gz", hash = "sha256:46a67b99925ee3d53914331759c6ee110b31bb790b74b05915acfca41074c206", size = 104508, upload-time = "2025-06-14T20:57:08.731Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/95/cb/282d59421cdb89a5e5fcce72fc37d6eeace98a2a86d71f3be3cd47801298/pyobjc_framework_corelocation-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:562e31124f80207becfd8df01868f73fa5aa70169cc4460e1209fb16916e4fb4", size = 12752, upload-time = "2025-06-14T20:47:43.273Z" },
    { url = "https://files.pythonhosted.org/packages/de/cb/c4672fcfa5e998cfd0dd165717ec312f7e6cbac06ecb4a0e227dbc4d7e27/pyobjc_framework_corelocation-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0f8182835429118a55ed65963c80f5b2892d190747b986e8395b1cd99f41a1d0", size = 12768, upload-time = "2025-06-14T20:47:43.987Z" },
    { url = "https://files.pythonhosted.org/packages/47/e7/ef83b4d6fca57bd09a56064fdcb55792b7497279b1dac3de781c86ed40ec/pyobjc_framework_corelocation-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bc3f27802415aa62330a2d2507adc3a9b98a89d6de7d1033ebe6b8c461610831", size = 12910, upload-time = "2025-06-14T20:47:44.744Z" },
    { url = "https://files.pythonhosted.org/packages/a3/9f/9a107d223babd3d846873bd30897d4411585523403adfaec91963abcb281/pyobjc_framework_corelocation-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:17ce2530bd5a0dca9059eb11bc647d920490bcdd35b5cac1e160f51f0297bdc8", size = 12800, upload-time = "2025-06-14T20:47:45.477Z" },
    { url = "https://files.pythonhosted.org/packages/0d/54/3a841006c2bf0fa4797c2fb77c79150b526800d191a539a8f2d0e54a377e/pyobjc_framework_corelocation-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a384d9fcba2c041d8f8115b51a07ef11c391bc30f72560aaea8b94db6b3b225c", size = 12953, upload-time = "2025-06-14T20:47:46.499Z" },
]

[[package]]
name = "pyobjc-framework-coremedia"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/95/5d/81513acd219df77a89176f1574d936b81ad6f6002225cabb64d55efb7e8d/pyobjc_framework_coremedia-11.1.tar.gz", hash = "sha256:82cdc087f61e21b761e677ea618a575d4c0dbe00e98230bf9cea540cff931db3", size = 216389, upload-time = "2025-06-14T20:57:09.546Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/d1/b3d004d6a2d2188d196779d92fe8cfa2533f5722cd216fbc4f0cffc63b24/pyobjc_framework_coremedia-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ea5055298af91e463ffa7977d573530f9bada57b8f2968dcc76a75e339b9a598", size = 29015, upload-time = "2025-06-14T20:47:49.655Z" },
    { url = "https://files.pythonhosted.org/packages/1c/23/cafd29011d14eac27fc55770157ebb8e02ffed9f75e01f24e97616417c4c/pyobjc_framework_coremedia-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7ecdb64c743ffe9fd3949c7cc9109891b9f399a0852717fcb969d33c4e7ba527", size = 29031, upload-time = "2025-06-14T20:47:50.395Z" },
    { url = "https://files.pythonhosted.org/packages/de/a6/ca85b7d9d000e8e2748bcacde356278cb90f6ca9aed54dce6a42d1716ba8/pyobjc_framework_coremedia-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:969ce357c616f6835f47e27d1e73964374cdb671476571dfd358894a8ced06f2", size = 29094, upload-time = "2025-06-14T20:47:51.318Z" },
    { url = "https://files.pythonhosted.org/packages/b8/3d/56d530cf504a6eef84f51c8f6f845af8b947f6108e41db5e0b5189d5a667/pyobjc_framework_coremedia-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bf1da05c297776c297ab3489ebf18d954efdff530acbdd6e70c32be811e20ec6", size = 29043, upload-time = "2025-06-14T20:47:52.092Z" },
    { url = "https://files.pythonhosted.org/packages/a4/bc/b237ecd4954a0f07450469236ca45412edb7d8715ff7fc175ac519e7c472/pyobjc_framework_coremedia-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:aa942d9ad0cf5bc4d3ede8779c3fac2f04cf3857687f2fb8505bae3378d04b95", size = 29111, upload-time = "2025-06-14T20:47:53.083Z" },
]

[[package]]
name = "pyobjc-framework-coremediaio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/64/68/9cef2aefba8e69916049ff43120e8794df8051bdf1f690a55994bbe4eb57/pyobjc_framework_coremediaio-11.1.tar.gz", hash = "sha256:bccd69712578b177144ded398f4695d71a765ef61204da51a21f0c90b4ad4c64", size = 108326, upload-time = "2025-06-14T20:57:10.435Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/b5/5dd941c1d7020a78b563a213fb8be7c6c3c1073c488914e158cd5417f4f7/pyobjc_framework_coremediaio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:39ad2518de9943c474e5ca0037e78f92423c3352deeee6c513a489016dac1266", size = 17250, upload-time = "2025-06-14T20:47:56.505Z" },
    { url = "https://files.pythonhosted.org/packages/08/44/cd98e1dacdd28c4e80fe1b0dde3a5171494735cb4a7b8b5775825b824b96/pyobjc_framework_coremediaio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e0a079fe790ce8a69d11bea46b315c9a0d3f3999a2f09e2ef4fcc4430a47c42", size = 17226, upload-time = "2025-06-14T20:47:57.267Z" },
    { url = "https://files.pythonhosted.org/packages/f9/66/89a3c01d1d1a0e7b510ade14a2c604883d6846d8279095ff4849f9989f9c/pyobjc_framework_coremediaio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5a94f9e507b470ce7dcb887e79ccf19e98693a606ad34462d711004e3edd88c3", size = 17564, upload-time = "2025-06-14T20:47:58.483Z" },
    { url = "https://files.pythonhosted.org/packages/2b/70/4a137a8a8b618ad025586ebe7f459989ead666e41825053d297c1a104f72/pyobjc_framework_coremediaio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0a7ffded00a7dc6f0bf4a44a6832f0150d45a83886486148b71ccc67c70ef215", size = 17257, upload-time = "2025-06-14T20:47:59.244Z" },
    { url = "https://files.pythonhosted.org/packages/1b/d7/054313e96c40efe8f535ef1a172cc612c53a55f27eb5e2805a84727155d6/pyobjc_framework_coremediaio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5ff161025ef28d5e2eed90db0e8b828cb361281b799b16b1885711ca0addc1aa", size = 17572, upload-time = "2025-06-14T20:48:00.01Z" },
]

[[package]]
name = "pyobjc-framework-coremidi"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/06/ca/2ae5149966ccd78290444f88fa62022e2b96ed2fddd47e71d9fd249a9f82/pyobjc_framework_coremidi-11.1.tar.gz", hash = "sha256:095030c59d50c23aa53608777102bc88744ff8b10dfb57afe24b428dcd12e376", size = 107817, upload-time = "2025-06-14T20:57:11.245Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c2/2d/57c279dd74a9073d1416b10b05ebb9598f4868cad010d87f46ef4b517324/pyobjc_framework_coremidi-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:deb9120478a831a898f22f68737fc683bb9b937e77556e78b75986aebd349c55", size = 24277, upload-time = "2025-06-14T20:48:03.184Z" },
    { url = "https://files.pythonhosted.org/packages/1e/66/dfdc7a5dc5a44b1660015bb24454ca0cbdf436e631e39917c495475dbb24/pyobjc_framework_coremidi-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2e1ab122501206ceae07123fdc433e91a5f1a97224f80ece0717b6f36ad2029", size = 24308, upload-time = "2025-06-14T20:48:04.285Z" },
    { url = "https://files.pythonhosted.org/packages/46/fe/200f286d5506efdc6c6d150eda24909a89f5c856a7a5003db0a423f66943/pyobjc_framework_coremidi-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:3462a158214adb7ebc785fb6924e674c58dcd471888dbca5e2e77381f3f1bbdc", size = 24463, upload-time = "2025-06-14T20:48:05.014Z" },
    { url = "https://files.pythonhosted.org/packages/7e/a5/053ad95a662544ef036c18d45680a4016b9eb897fb7dfcbcef13602b947a/pyobjc_framework_coremidi-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f4b70864cae295f27b5d51817c0768fade7c1335a59410910146e5f2a54c475c", size = 24320, upload-time = "2025-06-14T20:48:06.104Z" },
    { url = "https://files.pythonhosted.org/packages/7d/2c/e97e4f8ea07ffca82daa0ed0159f6d5ca03699b2a1944f4c4adb4d64bd21/pyobjc_framework_coremidi-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2ef1a10f6230fce82b931670470158404657d9fb9ac558a77b46b547e9978524", size = 24474, upload-time = "2025-06-14T20:48:06.847Z" },
]

[[package]]
name = "pyobjc-framework-coreml"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0d/5d/4309f220981d769b1a2f0dcb2c5c104490d31389a8ebea67e5595ce1cb74/pyobjc_framework_coreml-11.1.tar.gz", hash = "sha256:775923eefb9eac2e389c0821b10564372de8057cea89f1ea1cdaf04996c970a7", size = 82005, upload-time = "2025-06-14T20:57:12.004Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3e/9e/a1b6d30b4f91c7cc4780e745e1e73a322bd3524a773f66f5eac0b1600d85/pyobjc_framework_coreml-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c768b03d72488b964d753392e9c587684961d8237b69cca848b3a5a00aea79c9", size = 11436, upload-time = "2025-06-14T20:48:10.048Z" },
    { url = "https://files.pythonhosted.org/packages/95/95/f8739958ccf7cbaaf172653b3665cfcee406c5503a49828130b618b93d3f/pyobjc_framework_coreml-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:10d51f8a5fe8d30c7ec70304a2324df76b48b9fbef30ee0f0c33b99a49ae8853", size = 11452, upload-time = "2025-06-14T20:48:10.74Z" },
    { url = "https://files.pythonhosted.org/packages/57/d1/881cef8f09f022ba6534d98f0bb1c3ad5e68dbdda91173d88fa1524c0526/pyobjc_framework_coreml-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4df25ee233430f016ffcb4e88506b54c8e7b668c93197e6a1341761530a5922c", size = 11682, upload-time = "2025-06-14T20:48:11.421Z" },
    { url = "https://files.pythonhosted.org/packages/cf/92/81be40d2b4a9a52e75ff0051dfd9258cf5aad529d86144f0730d1f7ec034/pyobjc_framework_coreml-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:287a2a059016d02d8c40e0d29e70226142a4969db97ad79cefc70ec9bf0ab29e", size = 11551, upload-time = "2025-06-14T20:48:12.425Z" },
    { url = "https://files.pythonhosted.org/packages/b7/08/bb686f0ede51d1e09be395f176613ee4834f47ce081c13e4ee464d14c748/pyobjc_framework_coreml-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a479c3d759aff3695f72c7915a78df6e92e0eca7027abaa8b4a07e876ba1dbfb", size = 11729, upload-time = "2025-06-14T20:48:13.135Z" },
]

[[package]]
name = "pyobjc-framework-coremotion"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a5/95/e469dc7100ea6b9c29a074a4f713d78b32a78d7ec5498c25c83a56744fc2/pyobjc_framework_coremotion-11.1.tar.gz", hash = "sha256:5884a568521c0836fac39d46683a4dea3d259a23837920897042ffb922d9ac3e", size = 67050, upload-time = "2025-06-14T20:57:12.705Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e9/17/ffa3cf9fde9df31f3d6ecb38507c61c6d8d81276d0a9116979cafd5a0ab7/pyobjc_framework_coremotion-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8c3b33228a170bf8495508a8923451ec600435c7bff93d7614f19c913baeafd1", size = 10368, upload-time = "2025-06-14T20:48:16.066Z" },
    { url = "https://files.pythonhosted.org/packages/7c/2b/ade312f6bda6c368112bc2151834e664c22ae7d6d1f2ce33347b84ece7fb/pyobjc_framework_coremotion-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ac5302deaab99a7443cad63f125061a90040852d4f8efb58492542a612b2afe3", size = 10393, upload-time = "2025-06-14T20:48:16.784Z" },
    { url = "https://files.pythonhosted.org/packages/63/51/380d1b2b072b379a4740b725bdec4119c0c82bc66c55a2a62ca2fa0ec478/pyobjc_framework_coremotion-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d67413a56989154dab7bf1b69c14b0b2387d87d3a4c8e3c8a9fc0230f061e8ab", size = 10534, upload-time = "2025-06-14T20:48:17.466Z" },
    { url = "https://files.pythonhosted.org/packages/03/4f/efbab9157e74d39074a3ce05e0494174203cbdb28a48c59fb2464b0fffed/pyobjc_framework_coremotion-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:42fb307b86999d078503ff79bdf8df4d1c27d38763db6b1c5c0f4054241f67a3", size = 10443, upload-time = "2025-06-14T20:48:18.532Z" },
    { url = "https://files.pythonhosted.org/packages/78/90/1da8d8acbcd8fe348bd2e94a26e5f289e621af1d42f86c57b4d3de940650/pyobjc_framework_coremotion-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:708431c53f483bc6da199375227ffea1b4e8e7d8c81d162492db3fc36893fb53", size = 10606, upload-time = "2025-06-14T20:48:19.228Z" },
]

[[package]]
name = "pyobjc-framework-coreservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-fsevents" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a8/a9/141d18019a25776f507992f9e7ffc051ca5a734848d8ea8d848f7c938efc/pyobjc_framework_coreservices-11.1.tar.gz", hash = "sha256:cf8eb5e272c60a96d025313eca26ff2487dcd02c47034cc9db39f6852d077873", size = 1245086, upload-time = "2025-06-14T20:57:13.914Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fa/0f/52827197a1fa1dabefd77803920eaf340f25e0c81944844ab329d511cade/pyobjc_framework_coreservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6bd313ec326efd715b4b10c3ebcc9f054e3ee3178be407b97ea225cd871351d2", size = 30252, upload-time = "2025-06-14T20:48:22.657Z" },
    { url = "https://files.pythonhosted.org/packages/9d/dc/8a0414dd81054062a56a54db5c1cbb35c715081c9210ed69d5fed8046ebe/pyobjc_framework_coreservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8aee505dca56afc5363d8d0dff0b2d26583a8d0f3ac37674cef86f66c51a2934", size = 30271, upload-time = "2025-06-14T20:48:23.427Z" },
    { url = "https://files.pythonhosted.org/packages/44/e3/494bbc589b0a02ad7ab657fdf67359298b007112b65a2f4416d61176a4c4/pyobjc_framework_coreservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4ffa188322ab9d05c6964926959dedba5cc04534232f1eff03aee5f09faa499e", size = 30282, upload-time = "2025-06-14T20:48:24.175Z" },
    { url = "https://files.pythonhosted.org/packages/ab/0b/1c666c01c003e1b73baa5c71cab5a50000b1180e5c1cbf14b02f20cf8c3b/pyobjc_framework_coreservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21e9e86192d719cd5c899cc0e931110733da0b5bbbf606681e5fccd4dd39c174", size = 30294, upload-time = "2025-06-14T20:48:24.923Z" },
    { url = "https://files.pythonhosted.org/packages/ff/39/6026aaeef8b0eb0c25089374132a9bdbeffbc10f93cab589162efd43dc86/pyobjc_framework_coreservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:74dcc295245f07754328bada9577b189e3abef71607d013e939751c1b5b55729", size = 30309, upload-time = "2025-06-14T20:48:25.706Z" },
]

[[package]]
name = "pyobjc-framework-corespotlight"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/31/c7/b67ebfb63b7ccbfda780d583056d1fd4b610ba3839c8ebe3435b86122c61/pyobjc_framework_corespotlight-11.1.tar.gz", hash = "sha256:4dd363c8d3ff7619659b63dd31400f135b03e32435b5d151459ecdacea14e0f2", size = 87161, upload-time = "2025-06-14T20:57:14.934Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/f8/06b7edfeabe5b3874485b6e5bbe4a39d9f2e1f44348faa7cb320fbc6f21a/pyobjc_framework_corespotlight-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7cedd3792fe1fe2a8dc65a8ff1f70baf12415a5dc9dc4d88f987059567d7e694", size = 9977, upload-time = "2025-06-14T20:48:28.757Z" },
    { url = "https://files.pythonhosted.org/packages/7d/ce/812ae5a7f97a57abce1b2232280d5838a77d5454e5b05d79c3e654ad7400/pyobjc_framework_corespotlight-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:546d0d9b101de4ca20449f3807d1f88e5c26de0345a8bfefc70f12f87efb8433", size = 9997, upload-time = "2025-06-14T20:48:29.833Z" },
    { url = "https://files.pythonhosted.org/packages/5c/ee/9c432c1735f537c5b56dae43f6d2f2dd4922cac45c8e072e5a405b3ab81b/pyobjc_framework_corespotlight-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f562cc65865066f8e2e5d96c868fd7f463d8280f1ef01df85250fc1150feed0e", size = 10137, upload-time = "2025-06-14T20:48:30.513Z" },
    { url = "https://files.pythonhosted.org/packages/c1/b8/3a8910e0ffbec9f13f090be0e7cd40ad8144069dcdb80062f13c4768be5c/pyobjc_framework_corespotlight-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bce3d84f97014228b244c734aea3ec03b257573b22c097dff4eb176a80cd29a9", size = 10043, upload-time = "2025-06-14T20:48:31.218Z" },
    { url = "https://files.pythonhosted.org/packages/b5/7e/36e3342da3f5d05979729570c1630e442305118d5cb6462e81d21feb74e7/pyobjc_framework_corespotlight-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f59d0d2f0411db102d16490e47b457b994c613f1b980869fa3a151863da7aa4c", size = 10188, upload-time = "2025-06-14T20:48:31.906Z" },
]

[[package]]
name = "pyobjc-framework-coretext"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/65/e9/d3231c4f87d07b8525401fd6ad3c56607c9e512c5490f0a7a6abb13acab6/pyobjc_framework_coretext-11.1.tar.gz", hash = "sha256:a29bbd5d85c77f46a8ee81d381b847244c88a3a5a96ac22f509027ceceaffaf6", size = 274702, upload-time = "2025-06-14T20:57:16.059Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/32/67/9cc5189c366e67dc3e5b5976fac73cc6405841095f795d3fa0d5fc43d76a/pyobjc_framework_coretext-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1597bf7234270ee1b9963bf112e9061050d5fb8e1384b3f50c11bde2fe2b1570", size = 30175, upload-time = "2025-06-14T20:48:35.023Z" },
    { url = "https://files.pythonhosted.org/packages/b0/d1/6ec2ef4f8133177203a742d5db4db90bbb3ae100aec8d17f667208da84c9/pyobjc_framework_coretext-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:37e051e8f12a0f47a81b8efc8c902156eb5bc3d8123c43e5bd4cebd24c222228", size = 30180, upload-time = "2025-06-14T20:48:35.766Z" },
    { url = "https://files.pythonhosted.org/packages/0a/84/d4a95e49f6af59503ba257fbed0471b6932f0afe8b3725c018dd3ba40150/pyobjc_framework_coretext-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:56a3a02202e0d50be3c43e781c00f9f1859ab9b73a8342ff56260b908e911e37", size = 30768, upload-time = "2025-06-14T20:48:36.869Z" },
    { url = "https://files.pythonhosted.org/packages/64/4c/16e1504e06a5cb23eec6276835ddddb087637beba66cf84b5c587eba99be/pyobjc_framework_coretext-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:15650ba99692d00953e91e53118c11636056a22c90d472020f7ba31500577bf5", size = 30155, upload-time = "2025-06-14T20:48:37.948Z" },
    { url = "https://files.pythonhosted.org/packages/ad/a4/cbfa9c874b2770fb1ba5c38c42b0e12a8b5aa177a5a86d0ad49b935aa626/pyobjc_framework_coretext-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fb27f66a56660c31bb956191d64b85b95bac99cfb833f6e99622ca0ac4b3ba12", size = 30768, upload-time = "2025-06-14T20:48:38.734Z" },
]

[[package]]
name = "pyobjc-framework-corewlan"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c6/d8/03aff3c75485fc999e260946ef1e9adf17640a6e08d7bf603d31cfcf73fc/pyobjc_framework_corewlan-11.1.tar.gz", hash = "sha256:4a8afea75393cc0a6fe696e136233aa0ed54266f35a47b55a3583f4cb078e6ce", size = 65792, upload-time = "2025-06-14T20:57:16.931Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/09/8a/74feabaad1225eb2c44d043924ed8caea31683e6760cd9b918b8d965efea/pyobjc_framework_corewlan-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7bd0775d2466ad500aad4747d8a889993db3a14240239f30ef53c087745e9c8e", size = 10016, upload-time = "2025-06-14T20:48:41.792Z" },
    { url = "https://files.pythonhosted.org/packages/ef/12/792146e163aa4504bc7870c77c4ec2425f9a05fa615a2b5c9cbec89b0fc6/pyobjc_framework_corewlan-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c66643a97fcf3aa797fda997a3afc28d8d9bba9727dd5c0e68a313899d780f7", size = 10026, upload-time = "2025-06-14T20:48:42.626Z" },
    { url = "https://files.pythonhosted.org/packages/d8/e8/e0bf4c66192e85fb92a3ae01b50e34f2283568f7a0e5548f52db81b8b146/pyobjc_framework_corewlan-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6dc28264b56b18096c8869cce3f85e519fd27936f19524bb77458572ccfd7518", size = 10178, upload-time = "2025-06-14T20:48:43.309Z" },
    { url = "https://files.pythonhosted.org/packages/8e/c1/c860300f585de3f57b9f6c30c554e10708d57ec5ac1e920214b496638c0c/pyobjc_framework_corewlan-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:872de75409a710bb9a461e64e97185f8489d01898ec1b02c3e058c04606b61cf", size = 10051, upload-time = "2025-06-14T20:48:43.993Z" },
    { url = "https://files.pythonhosted.org/packages/ff/76/5bdb6b672d7b59a477cfcb35d7c0166a4bd86e7bc571ff693d62fccb75b2/pyobjc_framework_corewlan-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:14c7af9135ba0a920192af4dc50219bbf6185fcbb5de7041f097e1a1c8509587", size = 10210, upload-time = "2025-06-14T20:48:44.717Z" },
]

[[package]]
name = "pyobjc-framework-cryptotokenkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/eb/92/7fab6fcc6bb659d6946cfb2f670058180bcc4ca1626878b0f7c95107abf0/pyobjc_framework_cryptotokenkit-11.1.tar.gz", hash = "sha256:5f82f44d9ab466c715a7c8ad4d5ec47c68aacd78bd67b5466a7b8215a2265328", size = 59223, upload-time = "2025-06-14T20:57:17.658Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/f1/4cb9c90a55ec13301d60ac1c4d774c37b4ebc6db6331d3853021c933fcc8/pyobjc_framework_cryptotokenkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6384cb1d86fc586e2da934a5a37900825bd789e3a5df97517691de9af354af0c", size = 12543, upload-time = "2025-06-14T20:48:48.079Z" },
    { url = "https://files.pythonhosted.org/packages/c6/c8/b64a56ed65719b1dfb9c06da0772d4a76eceb830672aab237df745bc31f7/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a55c0e57ab164aa5ce562e4d9e69026339067ecb4888638995690f1c43b79cfa", size = 12559, upload-time = "2025-06-14T20:48:49.115Z" },
    { url = "https://files.pythonhosted.org/packages/9a/32/bb53ae388a99927fee626ba2746d3a6ec388cbc14b8f4ce91a35dd6b55e2/pyobjc_framework_cryptotokenkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cb3e1bd344e794cb98343171b5501a1a3b75548ef5385bda3d5ec613c0b98045", size = 12742, upload-time = "2025-06-14T20:48:49.837Z" },
    { url = "https://files.pythonhosted.org/packages/4a/34/9f30580ccddff6b6555603af920ef61a420ba515eb8ab7e10fbd9c1464a5/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:faab9493e36095c0257598e25ef81c50bcdb3afb5843a82e6dfad8c7d1f47bcf", size = 12531, upload-time = "2025-06-14T20:48:51.634Z" },
    { url = "https://files.pythonhosted.org/packages/4e/07/baec88c0cfe9cd327753ce527dfab3b622bb5e2b45d3ff5bb8f4d2dae40c/pyobjc_framework_cryptotokenkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:efd89e5b024475701f6e9bec4cf1c2563e1bab37e79288397e09d9ad4e53d174", size = 12734, upload-time = "2025-06-14T20:48:52.396Z" },
]

[[package]]
name = "pyobjc-framework-datadetection"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7d/4d/65c61d8878b44689e28d5729be9edbb73e20b1b0500d1095172cfd24aea6/pyobjc_framework_datadetection-11.1.tar.gz", hash = "sha256:cbe0080b51e09b2f91eaf2a9babec3dcf2883d7966bc0abd8393ef7abfcfc5db", size = 13485, upload-time = "2025-06-14T20:57:18.829Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/08/c4/ef2136e4e0cc69b02479295822aa33c8e26995b265c8a1184867b65a0a06/pyobjc_framework_datadetection-11.1-py2.py3-none-any.whl", hash = "sha256:5afd3dde7bba3324befb7a3133c9aeaa5088efd72dccc0804267a74799f4a12f", size = 3482, upload-time = "2025-06-14T20:48:54.301Z" },
]

[[package]]
name = "pyobjc-framework-devicecheck"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f3/f2/b1d263f8231f815a9eeff15809f4b7428dacdc0a6aa267db5ed907445066/pyobjc_framework_devicecheck-11.1.tar.gz", hash = "sha256:8b05973eb2673571144d81346336e749a21cec90bd7fcaade76ffd3b147a0741", size = 13954, upload-time = "2025-06-14T20:57:19.782Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/39/72/17698a0d68b1067b20b32b4afd74bcafb53a7c73ae8fc608addc7b9e7a37/pyobjc_framework_devicecheck-11.1-py2.py3-none-any.whl", hash = "sha256:8edb36329cdd5d55e2c2c57c379cb5ba1f500f74a08fe8d2612b1a69b7a26435", size = 3668, upload-time = "2025-06-14T20:48:55.098Z" },
]

[[package]]
name = "pyobjc-framework-devicediscoveryextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9a/b8/102863bfa2f1e414c88bb9f51151a9a58b99c268a841b59d46e0dcc5fe6d/pyobjc_framework_devicediscoveryextension-11.1.tar.gz", hash = "sha256:ae160ea40f25d3ee5e7ce80ac9c1b315f94d0a4c7ccb86920396f71c6bf799a0", size = 14298, upload-time = "2025-06-14T20:57:20.738Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/67/89/fce0c0c89746f399d13e08b40fc12e29a2495f4dcebd30893336d047af18/pyobjc_framework_devicediscoveryextension-11.1-py2.py3-none-any.whl", hash = "sha256:96e5b13c718bd0e6c80fbd4e14b8073cffc88b3ab9bb1bbb4dab7893a62e4f11", size = 4249, upload-time = "2025-06-14T20:48:55.895Z" },
]

[[package]]
name = "pyobjc-framework-dictionaryservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d6/13/c46f6db61133fee15e3471f33a679da2af10d63fa2b4369e0cd476988721/pyobjc_framework_dictionaryservices-11.1.tar.gz", hash = "sha256:39c24452d0ddd037afeb73a1742614c94535f15b1c024a8a6cc7ff081e1d22e7", size = 10578, upload-time = "2025-06-14T20:57:21.392Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6c/86/4e757b4064a0feb8d60456672560adad0bb5df530ba6621fe65d175dbd90/pyobjc_framework_dictionaryservices-11.1-py2.py3-none-any.whl", hash = "sha256:92f4871066653f18e2394ac93b0a2ab50588d60020f6b3bd93e97b67cd511326", size = 3913, upload-time = "2025-06-14T20:48:56.806Z" },
]

[[package]]
name = "pyobjc-framework-discrecording"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a5/b2/d8d1a28643c2ab681b517647bacb68496c98886336ffbd274f0b2ad28cdc/pyobjc_framework_discrecording-11.1.tar.gz", hash = "sha256:37585458e363b20bb28acdb5cc265dfca934d8a07b7baed2584953c11c927a87", size = 123004, upload-time = "2025-06-14T20:57:22.01Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5e/17/032fa44bb66b6a20c432f3311072f88478b42dcf39b21ebb6c3bbdf2954f/pyobjc_framework_discrecording-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:e29bc8c3741ae52fae092f892de856dbab2363e71537a8ae6fd026ecb88e2252", size = 14581, upload-time = "2025-06-14T20:48:59.228Z" },
    { url = "https://files.pythonhosted.org/packages/55/d4/a9e2fa7aa38b4ecca9668b3ae9ae4244bf335974c42b46313c3ec631c73a/pyobjc_framework_discrecording-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2d18158366d124852ad58291954611ebdcc43263a3bb75d7fd273408e67720e2", size = 14592, upload-time = "2025-06-14T20:49:00.002Z" },
    { url = "https://files.pythonhosted.org/packages/5e/3c/660d06446b8e67121b755aeb20ba369234845675d25c658127e43fdbc835/pyobjc_framework_discrecording-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b027eca3a0391196d4335fcbd50c03ef1e8f5ce095411ed51a081328b4945bf5", size = 14763, upload-time = "2025-06-14T20:49:00.742Z" },
    { url = "https://files.pythonhosted.org/packages/31/bb/a1b694e9649b5148254325b3f78d658bb4919fc8d0d1c20c85313178b3da/pyobjc_framework_discrecording-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9cb36715bebdbbe1ad95e3c17359c2f5d3f6479a26b527ea1032154ca7cf3e09", size = 14623, upload-time = "2025-06-14T20:49:01.509Z" },
    { url = "https://files.pythonhosted.org/packages/62/25/e2552e4e8de09d8e8fe53f87cc0878c3cf2ff2030a6352a22d45a0484be8/pyobjc_framework_discrecording-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7c33421d6bed0993d9f1861dbf38b717b9a9e49dfb98fdf8b3cd8d558fdd50eb", size = 14799, upload-time = "2025-06-14T20:49:02.251Z" },
]

[[package]]
name = "pyobjc-framework-discrecordingui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-discrecording" },
]
sdist = { url = "https://files.pythonhosted.org/packages/25/53/d71717f00332b8fc3d8a5c7234fdc270adadfeb5ca9318a55986f5c29c44/pyobjc_framework_discrecordingui-11.1.tar.gz", hash = "sha256:a9f10e2e7ee19582c77f0755ae11a64e3d61c652cbd8a5bf52756f599be24797", size = 19370, upload-time = "2025-06-14T20:57:22.791Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4a/a6/505af43f7a17e0ca3d45e099900764e8758e0ca65341e894b74ade513556/pyobjc_framework_discrecordingui-11.1-py2.py3-none-any.whl", hash = "sha256:33233b87d7b85ce277a51d27acca0f5b38485cf1d1dc8e28a065910047766ee2", size = 4721, upload-time = "2025-06-14T20:49:03.737Z" },
]

[[package]]
name = "pyobjc-framework-diskarbitration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/da/2a/68fa0c99e04ec1ec24b0b7d6f5b7ec735d5e8a73277c5c0671438a69a403/pyobjc_framework_diskarbitration-11.1.tar.gz", hash = "sha256:a933efc6624779a393fafe0313e43378bcae2b85d6d15cff95ac30048c1ef490", size = 19866, upload-time = "2025-06-14T20:57:23.435Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/72/9534ca88effbf2897e07b722920b3f10890dbc780c6fff1ab4893ec1af10/pyobjc_framework_diskarbitration-11.1-py2.py3-none-any.whl", hash = "sha256:6a8e551e54df481a9081abba6fd680f6633babe5c7735f649731b22896bb6f08", size = 4849, upload-time = "2025-06-14T20:49:04.513Z" },
]

[[package]]
name = "pyobjc-framework-dvdplayback"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b8/76/77046325b1957f0cbcdf4f96667496d042ed4758f3413f1d21df5b085939/pyobjc_framework_dvdplayback-11.1.tar.gz", hash = "sha256:b44c36a62c8479e649133216e22941859407cca5796b5f778815ef9340a838f4", size = 64558, upload-time = "2025-06-14T20:57:24.118Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/59/0c/f0fefa171b6938010d87194e26e63eea5c990c33d2d7828de66802f57c36/pyobjc_framework_dvdplayback-11.1-py2.py3-none-any.whl", hash = "sha256:6094e4651ea29540ac817294b27e1596b9d1883d30e78fb5f9619daf94ed30cb", size = 8221, upload-time = "2025-06-14T20:49:05.297Z" },
]

[[package]]
name = "pyobjc-framework-eventkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/c4/cbba8f2dce13b9be37ecfd423ba2b92aa3f209dbb58ede6c4ce3b242feee/pyobjc_framework_eventkit-11.1.tar.gz", hash = "sha256:5643150f584243681099c5e9435efa833a913e93fe9ca81f62007e287349b561", size = 75177, upload-time = "2025-06-14T20:57:24.81Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/05/0a/384b9ff4c6380cac310cb7b92c145896c20a690192dbfc07b38909787ded/pyobjc_framework_eventkit-11.1-py2.py3-none-any.whl", hash = "sha256:c303207610d9c742f4090799f60103cede466002f3c89cf66011c8bf1987750b", size = 6805, upload-time = "2025-06-14T20:49:06.147Z" },
]

[[package]]
name = "pyobjc-framework-exceptionhandling"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/19/0d/c72a885b40d28a99b586447f9ea6f400589f13d554fcd6f13a2c841bb6d2/pyobjc_framework_exceptionhandling-11.1.tar.gz", hash = "sha256:e010f56bf60ab4e9e3225954ebb53e9d7135d37097043ac6dd2a3f35770d4efa", size = 17890, upload-time = "2025-06-14T20:57:25.521Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7f/81/dde9c73bf307b62c2d605fc818d3e49f857f39e0841766093dbc9ea47b08/pyobjc_framework_exceptionhandling-11.1-py2.py3-none-any.whl", hash = "sha256:31e6538160dfd7526ac0549bc0fce5d039932aea84c36abbe7b49c79ffc62437", size = 7078, upload-time = "2025-06-14T20:49:07.713Z" },
]

[[package]]
name = "pyobjc-framework-executionpolicy"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0b/cf/54431846508c5d5bb114a415ebb96187da5847105918169e42f4ca3b00e6/pyobjc_framework_executionpolicy-11.1.tar.gz", hash = "sha256:3280ad2f4c5eaf45901f310cee0c52db940c0c63e959ad082efb8df41055d986", size = 13496, upload-time = "2025-06-14T20:57:26.173Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a6/d2/cb192d55786d0f881f2fb60d45b61862a1fcade945f6a7a549ed62f47e61/pyobjc_framework_executionpolicy-11.1-py2.py3-none-any.whl", hash = "sha256:7d4141e572cb916e73bb34bb74f6f976a8aa0a396a0bffd1cf66e5505f7c76c8", size = 3719, upload-time = "2025-06-14T20:49:08.521Z" },
]

[[package]]
name = "pyobjc-framework-extensionkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ce/7d/89adf16c7de4246477714dce8fcffae4242778aecd0c5f0ad9904725f42c/pyobjc_framework_extensionkit-11.1.tar.gz", hash = "sha256:c114a96f13f586dbbab8b6219a92fa4829896a645c8cd15652a6215bc8ff5409", size = 19766, upload-time = "2025-06-14T20:57:27.106Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/90/2a/93105b5452d2ff680a47e38a3ec6f2a37164babd95e0ab976c07984366de/pyobjc_framework_extensionkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d505a64617c9db4373eb386664d62a82ba9ffc909bffad42cb4da8ca8e244c66", size = 7914, upload-time = "2025-06-14T20:49:11.842Z" },
    { url = "https://files.pythonhosted.org/packages/b8/67/1dbd000d9d0c17d838c471dbb48229fca1ca18fad8453c19ecc01d3312a1/pyobjc_framework_extensionkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:abbadbea5b18e4a6944c3c428753ee298a133cbf601c70e9586b14e3aebf649b", size = 7927, upload-time = "2025-06-14T20:49:12.542Z" },
    { url = "https://files.pythonhosted.org/packages/fb/35/e5d1e633ad5b0c5163afd19ac0b02740e47a45de78d6f2599de3bc6542a5/pyobjc_framework_extensionkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:5c2e203cb8134be1dd7df73d74c630adbaaf43d78eba04be451ea4f8bf582e22", size = 8069, upload-time = "2025-06-14T20:49:13.228Z" },
    { url = "https://files.pythonhosted.org/packages/9f/18/4c5ad3cbbf4f984f5316c2264789080d3caeaae47293cc739a59814f682f/pyobjc_framework_extensionkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3507f67dd06285c09bbdf5216a1148f5dd3a2f10eee7a9318dd14430bf6e67ee", size = 7974, upload-time = "2025-06-14T20:49:14.055Z" },
    { url = "https://files.pythonhosted.org/packages/75/1b/84ac20bb341a739681ad46ea0ec3d83b40f4716fa6ed966ad93274abe423/pyobjc_framework_extensionkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2767635e57b277e051719fa53c7683396ebdbcf3d40d44c1296758978ca8c92a", size = 8122, upload-time = "2025-06-14T20:49:14.76Z" },
]

[[package]]
name = "pyobjc-framework-externalaccessory"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d9/a3/519242e6822e1ddc9e64e21f717529079dbc28a353474420da8315d0a8b1/pyobjc_framework_externalaccessory-11.1.tar.gz", hash = "sha256:50887e948b78a1d94646422c243ac2a9e40761675e38b9184487870a31e83371", size = 23123, upload-time = "2025-06-14T20:57:27.845Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7d/1b/e2def12aca9162b0fe0bbf0790d35595d46b2ef12603749c42af9234ffca/pyobjc_framework_externalaccessory-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:00caf75b959db5d14118d78c04085e2148255498839cdee735a0b9f6ef86b6a2", size = 8903, upload-time = "2025-06-14T20:49:18.393Z" },
    { url = "https://files.pythonhosted.org/packages/b4/6f/1340c193c30ade7b0394b2c8f29f3e6dd501eb23a416a728cc9a23efaec2/pyobjc_framework_externalaccessory-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:50b796a4721db87863a28cd55668cb1547fcc28834afda2032e500cdab5b3d95", size = 8915, upload-time = "2025-06-14T20:49:19.076Z" },
    { url = "https://files.pythonhosted.org/packages/ec/27/1617435d3827a544c2ed2660ecd2e317c82cc8e819a55daa491973349e58/pyobjc_framework_externalaccessory-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:569124b686569c48e3855fff128f438a2b46af06280eac2a516aaa214ad325de", size = 9080, upload-time = "2025-06-14T20:49:19.772Z" },
    { url = "https://files.pythonhosted.org/packages/5b/cf/b825117308f1dcd82c7484d5ee7e3c9a2a00cd39b5bc2a73e43fd9803ceb/pyobjc_framework_externalaccessory-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:318772e698c6363e8c3c81229d93b639f5066a02a742ba1ab10cfdef3101d88b", size = 8961, upload-time = "2025-06-14T20:49:20.472Z" },
    { url = "https://files.pythonhosted.org/packages/a2/25/2b9aefc07e06df08501fbd3f3dc1da555e0943e9e169b842b6ac52505907/pyobjc_framework_externalaccessory-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d259724665617fc4f3e666d353b756a67cabb74e6f9d7b8f6f250a2d4bf05cb7", size = 9135, upload-time = "2025-06-14T20:49:21.149Z" },
]

[[package]]
name = "pyobjc-framework-fileprovider"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1b/80/3ebba2c1e5e3aeae989fe038c259a93e7e7e18fd56666ece514d000d38ea/pyobjc_framework_fileprovider-11.1.tar.gz", hash = "sha256:748ca1c75f84afdf5419346a24bf8eec44dca071986f31f00071dc191b3e9ca8", size = 91696, upload-time = "2025-06-14T20:57:28.546Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/49/b2/859d733b0110e56511478ba837fd8a7ba43aa8f8c7e5231b9e3f0258bfbf/pyobjc_framework_fileprovider-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6092dfe74c78c0b2abc03bfc18a0f5d8ddc624fc6a1d8dfef26d7796653072", size = 19622, upload-time = "2025-06-14T20:49:24.162Z" },
    { url = "https://files.pythonhosted.org/packages/91/ed/ae5ce4a18752ea2da5d7238f7847119af8c7dc69ffd9fb1369414c9745d2/pyobjc_framework_fileprovider-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9af41255df395a40a6e0b08c4410be5463f3ea91d8c9be61f6bd114252490ab2", size = 19627, upload-time = "2025-06-14T20:49:24.926Z" },
    { url = "https://files.pythonhosted.org/packages/84/83/530daae946318689d29457da995577996de5965ff41b4b3b8b604617ff46/pyobjc_framework_fileprovider-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d2720acdd582756ebda34418981e7646b7b85588b0b8fdafba7016eb657be6b8", size = 19859, upload-time = "2025-06-14T20:49:26.008Z" },
    { url = "https://files.pythonhosted.org/packages/e2/de/8411450fc602f841c7001651fc71487de6fc4d418beb5b83a576c734b0e5/pyobjc_framework_fileprovider-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0e48015bf50b3e56312c640ec6efde73cf3855e29b6d70d173a88957d9d74d27", size = 19970, upload-time = "2025-06-14T20:49:26.787Z" },
    { url = "https://files.pythonhosted.org/packages/d9/51/65d9be84e8c33c0341ed79392e9b9896a1f3ca21d96271d293389a94f264/pyobjc_framework_fileprovider-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:95ed3a03741076a4479aabb616b1e3ea022025a0ad842147a1200c27709019e2", size = 20211, upload-time = "2025-06-14T20:49:27.605Z" },
]

[[package]]
name = "pyobjc-framework-fileproviderui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-fileprovider" },
]
sdist = { url = "https://files.pythonhosted.org/packages/75/ed/0f5af06869661822c4a70aacd674da5d1e6b6661240e2883bbc7142aa525/pyobjc_framework_fileproviderui-11.1.tar.gz", hash = "sha256:162a23e67f59e1bb247e84dda88d513d7944d815144901a46be6fe051b6c7970", size = 13163, upload-time = "2025-06-14T20:57:29.568Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/01/667e139a0610494e181fccdce519f644166f3d8955b330674deba5876f0d/pyobjc_framework_fileproviderui-11.1-py2.py3-none-any.whl", hash = "sha256:f2765f114c2f4356aa41fb45c621fa8f0a4fae0b6d3c6b1a274366f5fe7fe829", size = 3696, upload-time = "2025-06-14T20:49:29.404Z" },
]

[[package]]
name = "pyobjc-framework-findersync"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2a/82/c6b670494ac0c4cf14cf2db0dfbe0df71925d20595404939383ddbcc56d3/pyobjc_framework_findersync-11.1.tar.gz", hash = "sha256:692364937f418f0e4e4abd395a09a7d4a0cdd55fd4e0184de85ee59642defb6e", size = 15045, upload-time = "2025-06-14T20:57:30.173Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/61/10/748ff914c5b7fbae5fa2436cd44b11caeabb8d2f6f6f1b9ab581f70f32af/pyobjc_framework_findersync-11.1-py2.py3-none-any.whl", hash = "sha256:c72b0fd8b746b99cfa498da36c5bb333121b2080ad73fa8cbea05cd47db1fa82", size = 4873, upload-time = "2025-06-14T20:49:30.194Z" },
]

[[package]]
name = "pyobjc-framework-fsevents"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8e/83/ec0b9ba355dbc34f27ed748df9df4eb6dbfdd9bbd614b0f193752f36f419/pyobjc_framework_fsevents-11.1.tar.gz", hash = "sha256:d29157d04124503c4dfa9dcbbdc8c34d3bab134d3db3a48d96d93f26bd94c14d", size = 29587, upload-time = "2025-06-14T20:57:30.796Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/c7/378d78e0fd956370f2b120b209117384b5b98925c6d8210a33fd73db4a15/pyobjc_framework_fsevents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b51d120b8f12a1ca94e28cf74113bf2bfd4c5aee7035b452e895518f4df7630", size = 13147, upload-time = "2025-06-14T20:49:33.022Z" },
    { url = "https://files.pythonhosted.org/packages/18/dc/3b7e75b9f8284257740679509b54f61da2a114cf805d7d3523053e4c6c19/pyobjc_framework_fsevents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fad5ada269f137afabd622b5fc04884c668ae1c7914a8791bab73b1d972f7713", size = 13164, upload-time = "2025-06-14T20:49:33.751Z" },
    { url = "https://files.pythonhosted.org/packages/dd/53/07d62a8642bfddee43cd96301abeed97e858757d363423cf6e383d91f900/pyobjc_framework_fsevents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ff064cfa9d9cffb5d4ab476fb5091604568744d961c670aced037b2b6f0d0185", size = 13525, upload-time = "2025-06-14T20:49:34.492Z" },
    { url = "https://files.pythonhosted.org/packages/54/1c/529de91b3ec8f8efc4bb3067678b3071f255637b17168e1d6f0132a8d729/pyobjc_framework_fsevents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9191ee2819f1d5dcae1559e4a66f19be03da3a103bccdc417e6888bcb5659f8f", size = 13047, upload-time = "2025-06-14T20:49:35.204Z" },
    { url = "https://files.pythonhosted.org/packages/67/21/f4e72a3761510abe93c089aa77b1f01bc1018ff47df1d09f430de9e1aea5/pyobjc_framework_fsevents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3289192f4d60e5b26f8ac88ae4049a11eff47caa6fb76ce34e3f7df405119905", size = 13501, upload-time = "2025-06-14T20:49:35.93Z" },
]

[[package]]
name = "pyobjc-framework-fskit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/46/47/d1f04c6115fa78936399a389cc5e0e443f8341c9a6c1c0df7f6fdbe51286/pyobjc_framework_fskit-11.1.tar.gz", hash = "sha256:9ded1eab19b4183cb04381e554bbbe679c1213fd58599d6fc6e135e93b51136f", size = 42091, upload-time = "2025-06-14T20:57:31.504Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/59/8f/db8f03688db77bfa4b78e89af1d89e910c5e877e94d58bdb3e93cc302e5d/pyobjc_framework_fskit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1e50b8f949f1386fada73b408463c87eb81ef7fd0b3482bacf0c206a73723013", size = 19948, upload-time = "2025-06-14T20:49:39.18Z" },
    { url = "https://files.pythonhosted.org/packages/7a/31/0dd6ad9dfce080d6e567326fe7243261740ef1090f72409322040f55a426/pyobjc_framework_fskit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:cc2390934a23b6407aa7802b11978374301444c3135835ad3373f7b4930c24eb", size = 19959, upload-time = "2025-06-14T20:49:39.941Z" },
    { url = "https://files.pythonhosted.org/packages/96/ba/8655c5959e28fc8b1806a0e0c0b6a47b615de586990efc8ff82a344177a3/pyobjc_framework_fskit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:44fe7b6781c8fd0552b13ab3d0ec21176cd7cd685a8a61d712f9e4e42eb2f736", size = 20201, upload-time = "2025-06-14T20:49:40.715Z" },
    { url = "https://files.pythonhosted.org/packages/18/ab/f576e3b078a3afe7930f6dbf8614d91ab08c3574bef970079c679c09c2e0/pyobjc_framework_fskit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:1d3793938e6d9b871483d4a6fad8f93d554bcbebd1fe7bed20e3f5d2feaa814b", size = 20166, upload-time = "2025-06-14T20:49:41.826Z" },
    { url = "https://files.pythonhosted.org/packages/6d/b2/42f72c4e6b0d61a393e66ea921c451bdfdfd6043cf24ae509018b336dbfb/pyobjc_framework_fskit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e38f9c449647109e5b14dc4a17f425efca10c7e539a3836ebdd1f9c0ef725a3b", size = 20437, upload-time = "2025-06-14T20:49:42.585Z" },
]

[[package]]
name = "pyobjc-framework-gamecenter"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1b/8e/b594fd1dc32a59462fc68ad502be2bd87c70e6359b4e879a99bcc4beaf5b/pyobjc_framework_gamecenter-11.1.tar.gz", hash = "sha256:a1c4ed54e11a6e4efba6f2a21ace92bcf186e3fe5c74a385b31f6b1a515ec20c", size = 31981, upload-time = "2025-06-14T20:57:32.192Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/99/52/0e56f21a6660a4f43882ec641b9e19b7ea92dc7474cec48cda1c9bed9c49/pyobjc_framework_gamecenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779cdf8f52348be7f64d16e3ea37fd621d5ee933c032db3a22a8ccad46d69c59", size = 18634, upload-time = "2025-06-14T20:49:45.737Z" },
    { url = "https://files.pythonhosted.org/packages/3e/fc/64a1e9dc4874a75ceed6e70bb07d5e2a3460283c7737e639a0408ec1b365/pyobjc_framework_gamecenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6ff8905a5a7bfd86cb2b95671b452be0836f79db065b8d8b3bb2a1a5750ffd0d", size = 18638, upload-time = "2025-06-14T20:49:46.826Z" },
    { url = "https://files.pythonhosted.org/packages/d5/0b/5a8559056ee1cd2fea7405d3843de900b410a14134c33eb112b9fa42201d/pyobjc_framework_gamecenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a73ca7027b2b827e26075b46551fe42425d4a68985022baa4413329a3a2c16ff", size = 18920, upload-time = "2025-06-14T20:49:47.61Z" },
    { url = "https://files.pythonhosted.org/packages/65/3a/b704f516ef405cb8911afd826fe775af6e06e22ce72bdd0e6c692e303b25/pyobjc_framework_gamecenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2a2cb6471d4d4b19f124c7e91a32882a0fab6e326bb0415915fd8f3b91cfc311", size = 18808, upload-time = "2025-06-14T20:49:48.354Z" },
    { url = "https://files.pythonhosted.org/packages/b4/c9/4759a330d40d10810b5ebf06286d44088e7c0ef5e4e5523d32045cc93495/pyobjc_framework_gamecenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:90132bb32f5ed6607e13c6f39346ad621611cb92cea308ced661a6ba1305b94e", size = 19093, upload-time = "2025-06-14T20:49:49.133Z" },
]

[[package]]
name = "pyobjc-framework-gamecontroller"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/70/4c/1dd62103092a182f2ab8904c8a8e3922d2b0a80a7adab0c20e5fd0207d75/pyobjc_framework_gamecontroller-11.1.tar.gz", hash = "sha256:4d5346faf90e1ebe5602c0c480afbf528a35a7a1ad05f9b49991fdd2a97f105b", size = 115783, upload-time = "2025-06-14T20:57:32.879Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/40/e3/e35bccb0284046ef716db4897b70d061b8b16c91fb2c434b1e782322ef56/pyobjc_framework_gamecontroller-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d2cbc0c6c7d9c63e6b5b0b124d0c2bad01bb4b136f3cbc305f27d31f8aab6083", size = 20850, upload-time = "2025-06-14T20:49:52.401Z" },
    { url = "https://files.pythonhosted.org/packages/ae/eb/42469724725f5d0f11c197aadbb0c5db1647ba69579df4e8d13f553bed1c/pyobjc_framework_gamecontroller-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4866b25df05f583af06095e7103ddd2fbb2484b0ac2c78fd2cd825f995e524fa", size = 20862, upload-time = "2025-06-14T20:49:53.47Z" },
    { url = "https://files.pythonhosted.org/packages/c3/43/7430884d24989c07e4e9394c905b02b3aedee7397960dd329a3c44e29c22/pyobjc_framework_gamecontroller-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:98f3f7afcbbe473a53537da42b2cdc0363df2647289eb66e8c762e4b46c23e73", size = 21108, upload-time = "2025-06-14T20:49:54.226Z" },
    { url = "https://files.pythonhosted.org/packages/69/55/5eb0027bfa985125ca152dd9720aec8e6d580689cc23326bc1a749c68133/pyobjc_framework_gamecontroller-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:de3892b8d09a65a3413d85a2f0762eba092afda8d97cbf9cda0417689cfb7027", size = 21281, upload-time = "2025-06-14T20:49:54.981Z" },
    { url = "https://files.pythonhosted.org/packages/7f/4f/8c32cf541b972a72e158bcdd1eb95f3180f2eb4532eee9fde8bc58f6961e/pyobjc_framework_gamecontroller-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:afe9f3aed8c900ebe63ee4f6e53c73c2fef7e503f6388afd39f46b31487f84a3", size = 21531, upload-time = "2025-06-14T20:49:55.749Z" },
]

[[package]]
name = "pyobjc-framework-gamekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5b/7b/ba141ec0f85ca816f493d1f6fe68c72d01092e5562e53c470a0111d9c34b/pyobjc_framework_gamekit-11.1.tar.gz", hash = "sha256:9b8db075da8866c4ef039a165af227bc29393dc11a617a40671bf6b3975ae269", size = 165397, upload-time = "2025-06-14T20:57:33.711Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/23/094e4fe38f2de029365604f0b7dffde7b0edfc57c3d388294c20ed663de2/pyobjc_framework_gamekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f945c7cfe53c4a349a03a1272f2736cc5cf88fe9e7a7a407abb03899635d860c", size = 21952, upload-time = "2025-06-14T20:49:58.933Z" },
    { url = "https://files.pythonhosted.org/packages/22/2c/9a35fb83a1df7588e2e60488aa425058ee7f01b5a9d4947f74f62a130bf3/pyobjc_framework_gamekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8c7f2bf7ecf44ca678cfdf76f23b32d9c2d03006a0af9ad8e60d9114d6be640a", size = 21968, upload-time = "2025-06-14T20:49:59.688Z" },
    { url = "https://files.pythonhosted.org/packages/7f/23/205eb0532238e79a56bab54820b0e39aedc546429e054dc12d55ca44bb23/pyobjc_framework_gamekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a7c8fce8a2c4614e3dd88b002540e67423e3efd41aa26d576db2de0fc61651b9", size = 22246, upload-time = "2025-06-14T20:50:00.462Z" },
    { url = "https://files.pythonhosted.org/packages/17/49/f297db34e3cdea78b03ec05bcf280b5afcefe7cb3b674705ca5705ee8bf1/pyobjc_framework_gamekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:555cb8d868fd2699ad70d4f9e7efccaa5df1995893050d05d478cb8f24dbf876", size = 22171, upload-time = "2025-06-14T20:50:01.723Z" },
    { url = "https://files.pythonhosted.org/packages/85/6e/5c886206d9b34870b66224e1a953afa431dd0c1247d29e5ae0606d06ad33/pyobjc_framework_gamekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:10331a69282b9554ce7ae618dc9ff68e96451759f6cfc687e188c82ba6b0e2ff", size = 22472, upload-time = "2025-06-14T20:50:02.814Z" },
]

[[package]]
name = "pyobjc-framework-gameplaykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-spritekit" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e0/07/f38b1d83eac10ea4f75c605ffc4850585740db89b90842d311e586ee36cd/pyobjc_framework_gameplaykit-11.1.tar.gz", hash = "sha256:9ae2bee69b0cc1afa0e210b4663c7cdbb3cc94be1374808df06f98f992e83639", size = 73399, upload-time = "2025-06-14T20:57:34.538Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e7/f5/65bdbefb9de7cbc2edf0b1f76286736536e31c216cfac1a5f84ea15f0fc1/pyobjc_framework_gameplaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0e4f34db8177b8b4d89fd22a2a882a6c9f6e50cb438ea2fbbf96845481bcd80d", size = 13091, upload-time = "2025-06-14T20:50:05.962Z" },
    { url = "https://files.pythonhosted.org/packages/25/4c/011e20a8e9ff1270d3efb6c470c3cd8af10dcd2b05042721b1a777aca7a6/pyobjc_framework_gameplaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:78c513bc53bafd996d896f6f4535f2700b4916013417f8b41f47045790c6208d", size = 13109, upload-time = "2025-06-14T20:50:06.7Z" },
    { url = "https://files.pythonhosted.org/packages/50/a1/31a50e79dfb9983b53220d0a1148a05544062829af76a20febfa2def0b41/pyobjc_framework_gameplaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:30e15e4e8df9b1c0ca92bfabf79f6b12a286e544e67762b14dd3023c53e41978", size = 13316, upload-time = "2025-06-14T20:50:07.431Z" },
    { url = "https://files.pythonhosted.org/packages/8d/8c/240c75848df95c29ce1c8aec1e2ac163f0405bcd6456c55075e438fbc92d/pyobjc_framework_gameplaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4dbea3471b5d4a82b37ddca41bfddd63380c31050de7392e2467fabebcd110b8", size = 13122, upload-time = "2025-06-14T20:50:08.172Z" },
    { url = "https://files.pythonhosted.org/packages/9c/1a/6590c96f57cda822620e66d8e21b5e55a62b14d040f38b0920f21645109e/pyobjc_framework_gameplaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:51abecafc1b55fcc9a5d73c078ea2d5a75964e0facf2c867a25d7f4f40238331", size = 13333, upload-time = "2025-06-14T20:50:09.468Z" },
]

[[package]]
name = "pyobjc-framework-healthkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/af/66/fa76f7c8e36e4c10677d42d91a8e220c135c610a06b759571db1abe26a32/pyobjc_framework_healthkit-11.1.tar.gz", hash = "sha256:20f59bd9e1ffafe5893b4eff5867fdfd20bd46c3d03bc4009219d82fc6815f76", size = 202009, upload-time = "2025-06-14T20:57:35.285Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c7/08/12fca070ad2dc0b9c311df209b9b6d275ee192cb5ccbc94616d9ddd80d88/pyobjc_framework_healthkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ab4350f9fe65909107dd7992b367a6c8aac7dc31ed3d5b52eeb2310367d0eb0b", size = 20311, upload-time = "2025-06-14T20:50:13.271Z" },
    { url = "https://files.pythonhosted.org/packages/5d/26/0337f1b4607a3a13a671a6b07468726943e0d28a462998fcd902f7df6fbf/pyobjc_framework_healthkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b6c739e17362897f0b1ba4aa4dc395b3d0c3855b87423eaeb6a89f910adc43f", size = 20330, upload-time = "2025-06-14T20:50:14.042Z" },
    { url = "https://files.pythonhosted.org/packages/f4/da/8681afc37504797f747c45be6780f2ef12b9c2a7703cda8f8cf9e48918ca/pyobjc_framework_healthkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2d1b76b04e9e33ac9441cafa695766938eac04f8c8c69f7efd93a6aceb6eca40", size = 20502, upload-time = "2025-06-14T20:50:14.788Z" },
    { url = "https://files.pythonhosted.org/packages/2e/7a/d8e9db3de92e432340d2b7c65dabace75650d426186658606acb5babc7c1/pyobjc_framework_healthkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:547ac283f84b5024be75290f351863f86eb48a950ec61e3150760230e6eba773", size = 20376, upload-time = "2025-06-14T20:50:15.536Z" },
    { url = "https://files.pythonhosted.org/packages/9d/9f/0ff955096171e5d7d57ca0b879b8771f52cd0f1d4cf0726cdfc0064884f3/pyobjc_framework_healthkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c693725d8476b745232df90ef01487e75e1e1c448e599dd34adf3dce859de760", size = 20544, upload-time = "2025-06-14T20:50:16.263Z" },
]

[[package]]
name = "pyobjc-framework-imagecapturecore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7b/3b/f4edbc58a8c7394393f8d00d0e764f655545e743ee4e33917f27b8c68e7b/pyobjc_framework_imagecapturecore-11.1.tar.gz", hash = "sha256:a610ceb6726e385b132a1481a68ce85ccf56f94667b6d6e1c45a2cfab806a624", size = 100398, upload-time = "2025-06-14T20:57:36.503Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/61/62/54ed61e7cd3213549c8e98ca87a6b21afbb428d2c41948ae48ea019bf973/pyobjc_framework_imagecapturecore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ed296c23d3d8d1d9af96a6486d09fb8d294cc318e4a2152e6f134151c76065f8", size = 16021, upload-time = "2025-06-14T20:50:19.836Z" },
    { url = "https://files.pythonhosted.org/packages/4e/91/71d48ec1b29d57112edd33ada86fcdbf1c9423ef2bdddadf8d37e8a03492/pyobjc_framework_imagecapturecore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ded8dc6a8c826a6ae1b6a6d0a31542bd1eb85345f86201689c54e51193b572dc", size = 16030, upload-time = "2025-06-14T20:50:20.568Z" },
    { url = "https://files.pythonhosted.org/packages/c7/9d/7452fecf9b362b7a384b44256ca388b3e99905376e6f594565f2b2be0761/pyobjc_framework_imagecapturecore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:254ae4502d651526c500533b8e2aee77ae7939f9acfd7d706dba2d464417deba", size = 16234, upload-time = "2025-06-14T20:50:21.341Z" },
    { url = "https://files.pythonhosted.org/packages/f9/37/b7207fd6f8d9b55d642ad73850148ae68c4877f993c5ae2f7eac2578b991/pyobjc_framework_imagecapturecore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bab8ed798598ddaa53f5b39707b58e16a1b1152858c87fd3fa0d64081f0c0364", size = 16115, upload-time = "2025-06-14T20:50:22.092Z" },
    { url = "https://files.pythonhosted.org/packages/6d/06/6eb5f2b1e2c8716ed07560055544f752ead2c2773dfc85cb24d9ec429b0e/pyobjc_framework_imagecapturecore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e01c29456d0560667f8fcd3ff2749e79ad51bf72512e699646ce32227f91b447", size = 16279, upload-time = "2025-06-14T20:50:22.82Z" },
]

[[package]]
name = "pyobjc-framework-inputmethodkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/02/32/6a90bba682a31960ba1fc2d3b263e9be26043c4fb7aed273c13647c8b7d9/pyobjc_framework_inputmethodkit-11.1.tar.gz", hash = "sha256:7037579524041dcee71a649293c2660f9359800455a15e6a2f74a17b46d78496", size = 27203, upload-time = "2025-06-14T20:57:37.246Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a8/0d/8a570072096fe339702e4ae9d98e59ee7c6c14124d4437c9a8c4482dda6d/pyobjc_framework_inputmethodkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd0c591a9d26967018a781fa4638470147ef2a9af3ab4a28612f147573eeefba", size = 9489, upload-time = "2025-06-14T20:50:25.875Z" },
    { url = "https://files.pythonhosted.org/packages/dc/a5/ce000bba1a52287c21d1d3aff6779a6bbb463da4337573cb17ecc9475939/pyobjc_framework_inputmethodkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:5095005809a4108f362998b46994f99b5a57f9ba367c01141c1b9eaea311bc5b", size = 9508, upload-time = "2025-06-14T20:50:26.577Z" },
    { url = "https://files.pythonhosted.org/packages/56/ad/bbdc9f4b91420a4d3cf0b633d1991d4ffb7bdeb78d01fa265bbd43fef929/pyobjc_framework_inputmethodkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:013919a4d766a7e66045fa5dd5d819bfa0450ccb59baba2b89d7449bce637d6b", size = 9667, upload-time = "2025-06-14T20:50:27.617Z" },
    { url = "https://files.pythonhosted.org/packages/13/92/d69e350213c242a2096f5708692effda0a0c96aab07410ecf582591b6f7f/pyobjc_framework_inputmethodkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2228bf58369351767294fe1aa400e98ec61e397a74a178788c24c98a1cff97ee", size = 9517, upload-time = "2025-06-14T20:50:28.333Z" },
    { url = "https://files.pythonhosted.org/packages/7f/b0/c6ee5412bb402f9c8ac9a0bbd471f4fd57a1d2ca9510480cb67d12ebaa8d/pyobjc_framework_inputmethodkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92b9ce788ce4b094e352a64508050ff8e24307b8670d33488304b941d118894e", size = 9696, upload-time = "2025-06-14T20:50:29.387Z" },
]

[[package]]
name = "pyobjc-framework-installerplugins"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4d/89/9a881e466476ca21f3ff3e8e87ccfba1aaad9b88f7eea4be6d3f05b07107/pyobjc_framework_installerplugins-11.1.tar.gz", hash = "sha256:363e59c7e05553d881f0facd41884f17b489ff443d7856e33dd0312064c746d9", size = 27451, upload-time = "2025-06-14T20:57:37.915Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/01/45c3d159d671c5f488a40f70aa6791b8483a3ed32b461800990bb5ab4bb3/pyobjc_framework_installerplugins-11.1-py2.py3-none-any.whl", hash = "sha256:f92b06c9595f3c800b7aabf1c1a235bfb4b2de3f5406d5f604d8e2ddd0aecb4e", size = 4798, upload-time = "2025-06-14T20:50:30.799Z" },
]

[[package]]
name = "pyobjc-framework-instantmessage"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9f/b9/5cec4dd0053b5f63c01211a60a286c47464d9f3e0c81bd682e6542dbff00/pyobjc_framework_instantmessage-11.1.tar.gz", hash = "sha256:c222aa61eb009704b333f6e63df01a0e690136e7e495907e5396882779bf9525", size = 33774, upload-time = "2025-06-14T20:57:38.553Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/91/34/acd618e90036822aaf01080d64558ba93e33e15ed91beb7d1d2aab290138/pyobjc_framework_instantmessage-11.1-py2.py3-none-any.whl", hash = "sha256:a70b716e279135eec5666af031f536c0f32dec57cfeae55cc9ff8457f10d4f3d", size = 5419, upload-time = "2025-06-14T20:50:31.993Z" },
]

[[package]]
name = "pyobjc-framework-intents"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/4c/af/d7f260d06b79acca8028e373c2fe30bf0be014388ba612f538f40597d929/pyobjc_framework_intents-11.1.tar.gz", hash = "sha256:13185f206493f45d6bd2d4903c2136b1c4f8b9aa37628309ace6ff4a906b4695", size = 448459, upload-time = "2025-06-14T20:57:39.589Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/37/e6fa5737da42fb1265041bd3bd4f2be96f09294018fabf07139dd9dbc7b9/pyobjc_framework_intents-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a663e2de1b7ae7b547de013f89773963f8180023e36f2cebfe8060395dc34c33", size = 32253, upload-time = "2025-06-14T20:50:35.028Z" },
    { url = "https://files.pythonhosted.org/packages/f0/ff/f793a0c4b5ea87af3fc228d74e457c1594695b2745b3007a8ef4832ebeb7/pyobjc_framework_intents-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e21b3bc33de2d5f69b5c1d581e5c724a08686fe84ec324a4be365bef769e482", size = 32266, upload-time = "2025-06-14T20:50:35.775Z" },
    { url = "https://files.pythonhosted.org/packages/52/e9/2725ae5f990faa7d7909e6ac14d14034d1e70028080ed602a03aa715b4bc/pyobjc_framework_intents-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e008d542abe38fd374c9ada7c833ad6e34a2db92b4dcbfba0a59ff830b9093bc", size = 32499, upload-time = "2025-06-14T20:50:36.531Z" },
    { url = "https://files.pythonhosted.org/packages/90/47/d934ec7c514cc59b53da271f172cf6fd30e9a63aa960580a751d4960d495/pyobjc_framework_intents-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:55498040123904b685cd38555eb84d95833fcb467b497d31757d6ac648a11817", size = 32506, upload-time = "2025-06-14T20:50:37.271Z" },
    { url = "https://files.pythonhosted.org/packages/95/f1/acbda130f45e38f35fca2aa381f4da9ed72e36c4c784395ddb3fea511391/pyobjc_framework_intents-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3ec70c02d3166088223938a7433e479659cbd8ce04be5bf515ea8d6e3c353d", size = 32742, upload-time = "2025-06-14T20:50:38.157Z" },
]

[[package]]
name = "pyobjc-framework-intentsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-intents" },
]
sdist = { url = "https://files.pythonhosted.org/packages/86/46/20aae4a71efb514b096f36273a6129b48b01535bf501e5719d4a97fcb3a5/pyobjc_framework_intentsui-11.1.tar.gz", hash = "sha256:c8182155af4dce369c18d6e6ed9c25bbd8110c161ed5f1b4fb77cf5cdb99d135", size = 21305, upload-time = "2025-06-14T20:57:40.477Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/43/7c/77fbd2a6f85eb905fbf27ba7540eaf2a026771ed5100fb1c01143cf47e9b/pyobjc_framework_intentsui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99a3ae40eb2a6ef1125955dd513c8acc88ce7d8d90130a8cdeaec8336e6fbec5", size = 8965, upload-time = "2025-06-14T20:50:41.281Z" },
    { url = "https://files.pythonhosted.org/packages/9b/d6/ce8e2f6354bd77271b8f9f2a05920fb0a6de57ab5d97033021672853acb5/pyobjc_framework_intentsui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:154fd92112184e8ef29ce81e685c377422dffcff4f7900ea6e5956a0e2be2268", size = 8983, upload-time = "2025-06-14T20:50:41.96Z" },
    { url = "https://files.pythonhosted.org/packages/e1/2b/562785a91c30eccd3eea28ea02b31a029e04ecc5e994da7cd60205baf250/pyobjc_framework_intentsui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6d7d5402c05840a45047cf905fa550c2898cf5580cdee00a36bd35dd624c7542", size = 9154, upload-time = "2025-06-14T20:50:42.651Z" },
    { url = "https://files.pythonhosted.org/packages/94/30/069cf617e514434304ea0b1e8227d653af192c6dc7062f2e97ab0204e449/pyobjc_framework_intentsui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35ef9f190f480147ce797809a63cc2b5f2ea64b51255d691e5e94bd8337e01ef", size = 9029, upload-time = "2025-06-14T20:50:43.353Z" },
    { url = "https://files.pythonhosted.org/packages/7a/77/6830682e3d7b9fdbead08f9053d714336f1cf5c6c6170d91b9cc266d243f/pyobjc_framework_intentsui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1bd950f808efb7ba7fbbc977300d7932a1dad41fbd3c78c8002870ca602e22d5", size = 9232, upload-time = "2025-06-14T20:50:44.031Z" },
]

[[package]]
name = "pyobjc-framework-iobluetooth"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/93/e0/74b7b10c567b66c5f38b45ab240336325a4c889f43072d90f2b90aaeb7c0/pyobjc_framework_iobluetooth-11.1.tar.gz", hash = "sha256:094fd4be60cd1371b17cb4b33a3894e0d88a11b36683912be0540a7d51de76f1", size = 300992, upload-time = "2025-06-14T20:57:41.256Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/da/94/eef57045762e955795a4e3312674045c52f8c506133acf9efe1b3370b93f/pyobjc_framework_iobluetooth-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:883781e7223cb0c63fab029d640721ded747f2e2b067645bc8b695ef02a4a4dd", size = 40406, upload-time = "2025-06-14T20:50:47.101Z" },
    { url = "https://files.pythonhosted.org/packages/ed/f5/24476d6919c2d8d849c88740e81f620663181b3c97ac6e3aaeb1833277a5/pyobjc_framework_iobluetooth-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:4a8b1caba9ac51435f64a6cf9c1a2be867603161af8bebdd1676072ebed2fed9", size = 40428, upload-time = "2025-06-14T20:50:47.85Z" },
    { url = "https://files.pythonhosted.org/packages/57/b6/ced1b076a86ea3d7a685155e8c61ab9ecf8037d2b5401d4aae65014789b3/pyobjc_framework_iobluetooth-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c99ade82a79263ea71c51d430696a2ad155beb01a67df59d52be63e181e0482", size = 40626, upload-time = "2025-06-14T20:50:48.655Z" },
    { url = "https://files.pythonhosted.org/packages/d2/a2/0567b8b6e5bb75f7172495890a7746a986fd46a436e5f1ca7abc386bbbdc/pyobjc_framework_iobluetooth-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2ef72cef1e03468e91a2f01af2390143bd6e4fcad1c6d0494dd857c99fa0d1a7", size = 40478, upload-time = "2025-06-14T20:50:49.418Z" },
    { url = "https://files.pythonhosted.org/packages/18/eb/b148fba594890aec937bf3a87b61a385918f2bee4394763595e59a9f39a0/pyobjc_framework_iobluetooth-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:a9a7e11a4bbb4a364b0412ca8632a1e853270c98c24d28421133f69c0c0ecaff", size = 40690, upload-time = "2025-06-14T20:50:50.174Z" },
]

[[package]]
name = "pyobjc-framework-iobluetoothui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-iobluetooth" },
]
sdist = { url = "https://files.pythonhosted.org/packages/dd/32/872272faeab6fe471eac6962c75db72ce65c3556e00b4edebdb41aaab7cb/pyobjc_framework_iobluetoothui-11.1.tar.gz", hash = "sha256:060c721f1cd8af4452493e8153b72b572edcd2a7e3b635d79d844f885afee860", size = 22835, upload-time = "2025-06-14T20:57:42.119Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d6/ed/35efed52ed3fa698480624e49ee5f3d859827aad5ff1c7334150c695e188/pyobjc_framework_iobluetoothui-11.1-py2.py3-none-any.whl", hash = "sha256:3c5a382d81f319a1ab9ab11b7ead04e53b758fdfeb604755d39c3039485eaac6", size = 4026, upload-time = "2025-06-14T20:50:52.018Z" },
]

[[package]]
name = "pyobjc-framework-iosurface"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c5/ce/38ec17d860d0ee040bb737aad8ca7c7ff46bef6c9cffa47382d67682bb2d/pyobjc_framework_iosurface-11.1.tar.gz", hash = "sha256:a468b3a31e8cd70a2675a3ddc7176ab13aa521c035f11188b7a3af8fff8b148b", size = 20275, upload-time = "2025-06-14T20:57:42.742Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/26/fa912d397b577ee318b20110a3c959e898514a1dce19b4f13f238a31a677/pyobjc_framework_iosurface-11.1-py2.py3-none-any.whl", hash = "sha256:0c36ad56f8ec675dd07616418a2bc29126412b54627655abd21de31bcafe2a79", size = 4948, upload-time = "2025-06-14T20:50:52.801Z" },
]

[[package]]
name = "pyobjc-framework-ituneslibrary"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ee/43/aebefed774b434965752f9001685af0b19c02353aa7a12d2918af0948181/pyobjc_framework_ituneslibrary-11.1.tar.gz", hash = "sha256:e2212a9340e4328056ade3c2f9d4305c71f3f6af050204a135f9fa9aa3ba9c5e", size = 47388, upload-time = "2025-06-14T20:57:43.383Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2a/57/a29150f734b45b7408cc06efb9e2156328ae74624e5c4a7fe95118e13e94/pyobjc_framework_ituneslibrary-11.1-py2.py3-none-any.whl", hash = "sha256:4e87d41f82acb6d98cf70ac3c932a568ceb3c2035383cbf177f54e63de6b815f", size = 5191, upload-time = "2025-06-14T20:50:53.637Z" },
]

[[package]]
name = "pyobjc-framework-kernelmanagement"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1a/b6/708f10ac16425834cb5f8b71efdbe39b42c3b1009ac0c1796a42fc98cd36/pyobjc_framework_kernelmanagement-11.1.tar.gz", hash = "sha256:e934d1638cd89e38d6c6c5d4d9901b4295acee2d39cbfe0bd91aae9832961b44", size = 12543, upload-time = "2025-06-14T20:57:44.046Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/cf/17ff988ad1a0e55a4be5336c64220aa620ad19bb2f487a1122e9a864b29e/pyobjc_framework_kernelmanagement-11.1-py2.py3-none-any.whl", hash = "sha256:ec74690bd3383a7945c4a038cc4e1553ec5c1d2408b60e2b0003a3564bff7c47", size = 3656, upload-time = "2025-06-14T20:50:54.484Z" },
]

[[package]]
name = "pyobjc-framework-latentsemanticmapping"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/db/8a/4e54ee2bc77d59d770b287daf73b629e2715a2b3b31264d164398131cbad/pyobjc_framework_latentsemanticmapping-11.1.tar.gz", hash = "sha256:c6c3142301e4d375c24a47dfaeebc2f3d0fc33128a1c0a755794865b9a371145", size = 17444, upload-time = "2025-06-14T20:57:44.643Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/50/d62815b02968236eb46c33f0fb0f7293a32ef68d2ec50c397140846d4e42/pyobjc_framework_latentsemanticmapping-11.1-py2.py3-none-any.whl", hash = "sha256:57f3b183021759a100d2847a4d8aa314f4033be3d2845038b62e5e823d96e871", size = 5454, upload-time = "2025-06-14T20:50:55.658Z" },
]

[[package]]
name = "pyobjc-framework-launchservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2b/0a/a76b13109b8ab563fdb2d7182ca79515f132f82ac6e1c52351a6b02896a8/pyobjc_framework_launchservices-11.1.tar.gz", hash = "sha256:80b55368b1e208d6c2c58395cc7bc12a630a2a402e00e4930493e9bace22b7bb", size = 20446, upload-time = "2025-06-14T20:57:45.258Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/12/30/a4de9021fdef7db0b224cdc1eae75811d889dc1debdfafdabf8be7bd0fb9/pyobjc_framework_launchservices-11.1-py2.py3-none-any.whl", hash = "sha256:8b58f1156651058b2905c87ce48468f4799db86a7edf760e1897fedd057a3908", size = 3889, upload-time = "2025-06-14T20:50:56.484Z" },
]

[[package]]
name = "pyobjc-framework-libdispatch"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/be/89/7830c293ba71feb086cb1551455757f26a7e2abd12f360d375aae32a4d7d/pyobjc_framework_libdispatch-11.1.tar.gz", hash = "sha256:11a704e50a0b7dbfb01552b7d686473ffa63b5254100fdb271a1fe368dd08e87", size = 53942, upload-time = "2025-06-14T20:57:45.903Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ac/92/ff9ceb14e1604193dcdb50643f2578e1010c68556711cd1a00eb25489c2b/pyobjc_framework_libdispatch-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dc9a7b8c2e8a63789b7cf69563bb7247bde15353208ef1353fff0af61b281684", size = 15627, upload-time = "2025-06-14T20:50:59.055Z" },
    { url = "https://files.pythonhosted.org/packages/0f/10/5851b68cd85b475ff1da08e908693819fd9a4ff07c079da9b0b6dbdaca9c/pyobjc_framework_libdispatch-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c4e219849f5426745eb429f3aee58342a59f81e3144b37aa20e81dacc6177de1", size = 15648, upload-time = "2025-06-14T20:50:59.809Z" },
    { url = "https://files.pythonhosted.org/packages/1b/79/f905f22b976e222a50d49e85fbd7f32d97e8790dd80a55f3f0c305305c32/pyobjc_framework_libdispatch-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a9357736cb47b4a789f59f8fab9b0d10b0a9c84f9876367c398718d3de085888", size = 15912, upload-time = "2025-06-14T20:51:00.572Z" },
    { url = "https://files.pythonhosted.org/packages/ee/b0/225a3645ba2711c3122eec3e857ea003646643b4122bd98db2a8831740ff/pyobjc_framework_libdispatch-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cd08f32ea7724906ef504a0fd40a32e2a0be4d64b9239530a31767ca9ccfc921", size = 15655, upload-time = "2025-06-14T20:51:01.655Z" },
    { url = "https://files.pythonhosted.org/packages/e2/b5/ff49fb81f13c7ec48cd7ccad66e1986ccc6aa1984e04f4a78074748f7926/pyobjc_framework_libdispatch-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5d9985b0e050cae72bf2c6a1cc8180ff4fa3a812cd63b2dc59e09c6f7f6263a1", size = 15920, upload-time = "2025-06-14T20:51:02.407Z" },
]

[[package]]
name = "pyobjc-framework-libxpc"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6a/c9/7e15e38ac23f5bfb4e82bdf3b7ef88e2f56a8b4ad884009bc2d5267d2e1f/pyobjc_framework_libxpc-11.1.tar.gz", hash = "sha256:8fd7468aa520ff19915f6d793070b84be1498cb87224bee2bad1f01d8375273a", size = 49135, upload-time = "2025-06-14T20:57:46.59Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/be/8f/dfd8e1e1e461f857a1e50138e69b17c0e62a8dcaf7dea791cc158d2bf854/pyobjc_framework_libxpc-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c29b2df8d74ff6f489afa7c39f7c848c5f3d0531a6bbe704571782ee6c820084", size = 19573, upload-time = "2025-06-14T20:51:05.902Z" },
    { url = "https://files.pythonhosted.org/packages/00/fa/9ac86892294428a0eb532242a6fcbec565d0cf0e919924b6b7c064c8b196/pyobjc_framework_libxpc-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6862e63f565823d4eeb56f18f90a3ee8682c52a8d4bcd486d3535c9959464eda", size = 19578, upload-time = "2025-06-14T20:51:06.659Z" },
    { url = "https://files.pythonhosted.org/packages/44/2c/0b0bdc7847adf6ed653e846a98685346f70b1aaa187e37ddff2641cc54e2/pyobjc_framework_libxpc-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2df539d11b65e229f8436a3660d0d1dce2cc7ba571054c5b91350b836db22576", size = 20167, upload-time = "2025-06-14T20:51:07.423Z" },
    { url = "https://files.pythonhosted.org/packages/13/f0/b44b1b094eafe62d3af6e13098eae1f2a9a863661d3d60745a6a0b91b4c4/pyobjc_framework_libxpc-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4f3083fde3c366cc58bcdb2c183fae9c531fb556d35a495818019f1a5d85c24d", size = 19291, upload-time = "2025-06-14T20:51:08.154Z" },
    { url = "https://files.pythonhosted.org/packages/7f/e4/9b7d86a0aa15ef3b6893238d7634dcfc08b6a800cd61d8a607055224c955/pyobjc_framework_libxpc-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:654db8e822e60a1246d4d55c7127a140e10d6faa0da5a7366a16cc10def44deb", size = 19868, upload-time = "2025-06-14T20:51:09.296Z" },
]

[[package]]
name = "pyobjc-framework-linkpresentation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b9/76/22873be73f12a3a11ae57af13167a1d2379e4e7eef584de137156a00f5ef/pyobjc_framework_linkpresentation-11.1.tar.gz", hash = "sha256:a785f393b01fdaada6d7d6d8de46b7173babba205b13b44f1dc884b3695c2fc9", size = 14987, upload-time = "2025-06-14T20:57:47.277Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3d/59/23249e76e06e3c1a4f88acac7144999fae5a5a8ce4b90272d08cc0ac38ae/pyobjc_framework_linkpresentation-11.1-py2.py3-none-any.whl", hash = "sha256:018093469d780a45d98f4e159f1ea90771caec456b1599abcc6f3bf3c6873094", size = 3847, upload-time = "2025-06-14T20:51:10.817Z" },
]

[[package]]
name = "pyobjc-framework-localauthentication"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e5/27/9e3195f3561574140e9b9071a36f7e0ebd18f50ade9261d23b5b9df8fccd/pyobjc_framework_localauthentication-11.1.tar.gz", hash = "sha256:3cd48907c794bd414ac68b8ac595d83c7e1453b63fc2cfc2d2035b690d31eaa1", size = 40700, upload-time = "2025-06-14T20:57:47.931Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/91/db/59f118cc2658814c6b501b7360ca4fe6a82fd289ced5897b99787130ceef/pyobjc_framework_localauthentication-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:aa3815f936612d78e51b53beed9115c57ae2fd49500bb52c4030a35856e6569e", size = 10730, upload-time = "2025-06-14T20:51:13.487Z" },
    { url = "https://files.pythonhosted.org/packages/9f/8b/544cadc6ecf75def347e96cdae4caa955bc23f2bc314779cffe1e6ba9475/pyobjc_framework_localauthentication-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9c9446c017b13c8dcadf485b76ab1d7bc12099b504bf5c2df1aae33b5dc4ab2c", size = 10748, upload-time = "2025-06-14T20:51:14.198Z" },
    { url = "https://files.pythonhosted.org/packages/44/f9/4095b2caa4453971bd790b6aeda05967c22743e1f80e5bf6cb63ec419288/pyobjc_framework_localauthentication-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d5a2e1ea2fe8233dc244f6029d5d0c878102b2e0615cb4b81b2f30d9ee101fca", size = 10896, upload-time = "2025-06-14T20:51:14.892Z" },
    { url = "https://files.pythonhosted.org/packages/dd/0a/fd8cfcfd761792fd482b49d08f5a0bf6540ebb3de6baacb4a5de5c5ed635/pyobjc_framework_localauthentication-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f49c9dbbecfa0b0a7a633c60bda8179575e3685b6a696658a835c63afee90f9a", size = 10786, upload-time = "2025-06-14T20:51:15.958Z" },
    { url = "https://files.pythonhosted.org/packages/ec/87/5204ea53e0a945877c650205841f766bc7fca55ad81cd5bcb0a966fcdaa4/pyobjc_framework_localauthentication-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e41be8e2132d1517e597401c7858b22531db2e7760d898993acc03ea13edb834", size = 10930, upload-time = "2025-06-14T20:51:16.696Z" },
]

[[package]]
name = "pyobjc-framework-localauthenticationembeddedui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-localauthentication" },
]
sdist = { url = "https://files.pythonhosted.org/packages/29/7b/08c1e52487b07e9aee4c24a78f7c82a46695fa883113e3eece40f8e32d40/pyobjc_framework_localauthenticationembeddedui-11.1.tar.gz", hash = "sha256:22baf3aae606e5204e194f02bb205f244e27841ea7b4a4431303955475b4fa56", size = 14076, upload-time = "2025-06-14T20:57:48.557Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/51/3d/2aaa3a4f0e82f0ac95cc432a6079f6dc20aa18a66c9a87ac6128c70df9ef/pyobjc_framework_localauthenticationembeddedui-11.1-py2.py3-none-any.whl", hash = "sha256:3539a947b102b41ea6e40e7c145f27280d2f36a2a9a1211de32fa675d91585eb", size = 3973, upload-time = "2025-06-14T20:51:18.2Z" },
]

[[package]]
name = "pyobjc-framework-mailkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7e/7e/f22d733897e7618bd70a658b0353f5f897c583df04e7c5a2d68b99d43fbb/pyobjc_framework_mailkit-11.1.tar.gz", hash = "sha256:bf97dc44cb09b9eb9d591660dc0a41f077699976144b954caa4b9f0479211fd7", size = 32012, upload-time = "2025-06-14T20:57:49.173Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/bf/23/1897fc071e8e71bc0bef53bcb0d600eb1ed3bd6c4609f7257ddfe151d37a/pyobjc_framework_mailkit-11.1-py2.py3-none-any.whl", hash = "sha256:8e6026462567baba194468e710e83787f29d9e8c98ea0583f7b401ea9515966e", size = 4854, upload-time = "2025-06-14T20:51:18.978Z" },
]

[[package]]
name = "pyobjc-framework-mapkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-corelocation" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/57/f0/505e074f49c783f2e65ca82174fd2d4348568f3f7281c1b81af816cf83bb/pyobjc_framework_mapkit-11.1.tar.gz", hash = "sha256:f3a5016f266091be313a118a42c0ea4f951c399b5259d93639eb643dacc626f1", size = 165614, upload-time = "2025-06-14T20:57:50.362Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/0a/50aa2fba57499ff657cacb9ef1730006442e4f42d9a822dae46239603ecc/pyobjc_framework_mapkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:91976c6dbc8cbb020e059a0ccdeab8933184712f77164dbad5a5526c1a49599d", size = 22515, upload-time = "2025-06-14T20:51:21.439Z" },
    { url = "https://files.pythonhosted.org/packages/78/54/792f4d5848176753bfde8f10ac21b663981adf940243765edad45908cd55/pyobjc_framework_mapkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:0b6fa1c4fffc3ae91adb965731a0cc943b3b6e82c8f21919a53a68b43a67b534", size = 22534, upload-time = "2025-06-14T20:51:22.199Z" },
    { url = "https://files.pythonhosted.org/packages/07/0c/fd03986fc74c5e523e5ba824d3b4f0fd1f4a52720f28da93499787960317/pyobjc_framework_mapkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:1dc27d315849ac96647d13c82eeefce5d1d2db8c64767ce10bd3e77cbaad2291", size = 22759, upload-time = "2025-06-14T20:51:23.269Z" },
    { url = "https://files.pythonhosted.org/packages/15/e3/6040945ad0bfb9a065d007a5e16b07f8ae0423fcf4e097eba92eb8a143bb/pyobjc_framework_mapkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:fb9b1d8cd5c0e8a097438369771d296de808621bc6013aa0065bc83716f5bdb0", size = 22657, upload-time = "2025-06-14T20:51:24.01Z" },
    { url = "https://files.pythonhosted.org/packages/e2/07/eca78e240aa13c4e32ac4c6db158e059f375a2d240928e42c8e77f348ef0/pyobjc_framework_mapkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:fe4581f5370dc7a209c1135e9c664a5a78950d3f5c39613bfb15c1e02a6258f3", size = 22886, upload-time = "2025-06-14T20:51:24.803Z" },
]

[[package]]
name = "pyobjc-framework-mediaaccessibility"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8d/81/60412b423c121de0fa0aa3ef679825e1e2fe8b00fceddec7d72333ef564b/pyobjc_framework_mediaaccessibility-11.1.tar.gz", hash = "sha256:52479a998fec3d079d2d4590a945fc78c41fe7ac8c76f1964c9d8156880565a4", size = 18440, upload-time = "2025-06-14T20:57:51.126Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/99/a1/f4cbdf8478ad01859e2c8eef08e28b8a53b9aa4fe5d238a86bad29b73555/pyobjc_framework_mediaaccessibility-11.1-py2.py3-none-any.whl", hash = "sha256:cd07e7fc375ff1e8d225e0aa2bd9c2c1497a4d3aa5a80bfb13b08800fcd7f034", size = 4691, upload-time = "2025-06-14T20:51:26.596Z" },
]

[[package]]
name = "pyobjc-framework-mediaextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/09/fd214dc0cf3f3bc3f528815af4799c0cb7b4bf4032703b19ea63486a132b/pyobjc_framework_mediaextension-11.1.tar.gz", hash = "sha256:85a1c8a94e9175fb364c453066ef99b95752343fd113f08a3805cad56e2fa709", size = 58489, upload-time = "2025-06-14T20:57:51.796Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/56/78/2c2d8265851f6060dbf4434c21bd67bf569b8c3071ba1f257e43aae563a8/pyobjc_framework_mediaextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06cb19004413a4b08dd75cf1e5dadea7f2df8d15feeeb7adb529d0cf947fa789", size = 38859, upload-time = "2025-06-14T20:51:29.102Z" },
    { url = "https://files.pythonhosted.org/packages/e7/6b/1d3761316ca7df57700a68b28f7c00cc4f050b3f6debac2305219506d6b1/pyobjc_framework_mediaextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:40f1440ccc8da6deb80810866f8c807c17567db67b53e1576ea3a3b1330c85f9", size = 38870, upload-time = "2025-06-14T20:51:29.862Z" },
    { url = "https://files.pythonhosted.org/packages/15/e3/48f4ba724e31cb7adeaf5f9198ad5ab9cab45bcfc358b8af5759d8f79971/pyobjc_framework_mediaextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:29edab42d9ecd394ac26f2ae2dfd7e2118452fc60a5623843919c1e9659c9dbc", size = 39104, upload-time = "2025-06-14T20:51:30.956Z" },
    { url = "https://files.pythonhosted.org/packages/a7/f8/65cfc9e9be245a7524572b64655d809c9294ded599ebf068c7c1b73c6ecf/pyobjc_framework_mediaextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5efd284932ed0e7cfbca90a142b84a3966c73e51308688f8c230af41f9fb8c39", size = 38925, upload-time = "2025-06-14T20:51:31.712Z" },
    { url = "https://files.pythonhosted.org/packages/68/99/bdc2fa27576302b6b3a5b018579637251e4ba4620505254e7ebd79134ad1/pyobjc_framework_mediaextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:ca3a3ef1f3a759b53f297ccd701d29091eec66cc629a2b48c9acbe6c297bf256", size = 39142, upload-time = "2025-06-14T20:51:32.844Z" },
]

[[package]]
name = "pyobjc-framework-medialibrary"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/2b/06/11ff622fb5fbdd557998a45cedd2b0a1c7ea5cc6c5cb015dd6e42ebd1c41/pyobjc_framework_medialibrary-11.1.tar.gz", hash = "sha256:102f4326f789734b7b2dfe689abd3840ca75a76fb8058bd3e4f85398ae2ce29d", size = 18706, upload-time = "2025-06-14T20:57:52.474Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/2b/a4200080d97f88fdd406119bb8f00ccb7f32794f84735485510c14e87e76/pyobjc_framework_medialibrary-11.1-py2.py3-none-any.whl", hash = "sha256:779be84bd280f63837ce02028ca46b41b090902aa4205887ffd5777f49377669", size = 4340, upload-time = "2025-06-14T20:51:34.339Z" },
]

[[package]]
name = "pyobjc-framework-mediaplayer"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/80/d5/daba26eb8c70af1f3823acfd7925356acc4dd75eeac4fc86dc95d94d0e15/pyobjc_framework_mediaplayer-11.1.tar.gz", hash = "sha256:d07a634b98e1b9eedd82d76f35e616525da096bd341051ea74f0971e0f2f2ddd", size = 93749, upload-time = "2025-06-14T20:57:53.165Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2b/aa/b37aac80d821bd2fa347ddad1f6c7c75b23155e500edf1cb3b3740c27036/pyobjc_framework_mediaplayer-11.1-py2.py3-none-any.whl", hash = "sha256:b655cf537ea52d73209eb12935a047301c30239b318a366600f0f44335d51c9a", size = 6960, upload-time = "2025-06-14T20:51:35.171Z" },
]

[[package]]
name = "pyobjc-framework-mediatoolbox"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/68/cc230d2dfdeb974fdcfa828de655a43ce2bf4962023fd55bbb7ab0970100/pyobjc_framework_mediatoolbox-11.1.tar.gz", hash = "sha256:97834addc5179b3165c0d8cd74cc97ad43ed4c89547724216426348aca3b822a", size = 23568, upload-time = "2025-06-14T20:57:53.913Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b5/23/6b5d999e1e71c42d5d116d992515955ac1bbc5cf4890072bb26f38eb9802/pyobjc_framework_mediatoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2867c91645a335ee29b47e9c0e9fd3ea8c9daad0c0719c50b8bf244d76998056", size = 12785, upload-time = "2025-06-14T20:51:37.593Z" },
    { url = "https://files.pythonhosted.org/packages/29/05/24d60869a816418771653057720727d6df2dd8485302a21f80cfcb694110/pyobjc_framework_mediatoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bf26348d20caef38efb9cfc02d28af83c930b2f2c9581407f8ec04b3d8321a7a", size = 12794, upload-time = "2025-06-14T20:51:38.278Z" },
    { url = "https://files.pythonhosted.org/packages/37/c5/7b2950c22187c1a2e4f492684c34dd0cd230b8be4c7749e4b223b7769def/pyobjc_framework_mediatoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:975de470af8e52104bd1548eb9b4b0ef98524f35a6263c0bb4182797b9c5975b", size = 13394, upload-time = "2025-06-14T20:51:39.001Z" },
    { url = "https://files.pythonhosted.org/packages/d8/b4/f3b9944cb80bb5e72f3550ddfe6ba9fca81eefcb75abbf3410b304e0b1ca/pyobjc_framework_mediatoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d781e45fb1a7e532bcbae38c0f491629eaa641cdc226019544123b51794baf34", size = 12775, upload-time = "2025-06-14T20:51:39.745Z" },
    { url = "https://files.pythonhosted.org/packages/d3/6b/22f33982711fe787b2808530365afa2d4663d231200de51013cccc4cec46/pyobjc_framework_mediatoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e30fd2ffdea1b2c7c314d07266bce7614197c2b3ffd5b09f7012e7df7aa5c7a6", size = 13379, upload-time = "2025-06-14T20:51:41.235Z" },
]

[[package]]
name = "pyobjc-framework-metal"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/af/cf/29fea96fd49bf72946c5dac4c43ef50f26c15e9f76edd6f15580d556aa23/pyobjc_framework_metal-11.1.tar.gz", hash = "sha256:f9fd3b7574a824632ee9b7602973da30f172d2b575dd0c0f5ef76b44cfe9f6f9", size = 446549, upload-time = "2025-06-14T20:57:54.731Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4c/94/3d5a8bed000dec4a13e72dde175898b488192716b7256a05cc253c77020d/pyobjc_framework_metal-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1f3aae0f9a4192a7f4f158dbee126ab5ef63a81bf9165ec63bc50c353c8d0e6f", size = 57969, upload-time = "2025-06-14T20:51:45.051Z" },
    { url = "https://files.pythonhosted.org/packages/4f/af/b1f78770bb4b8d73d7a70140e39ca92daa2ba6b8de93d52b2ebf9db7d03e/pyobjc_framework_metal-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d9b24d0ddb98b34a9a19755e5ca507c62fcef40ee5eae017e39be29650137f8c", size = 57994, upload-time = "2025-06-14T20:51:46.209Z" },
    { url = "https://files.pythonhosted.org/packages/97/93/e680c0ece0e21cb20bc5d0504acd96ca6828fc766b8ed624d69230c1796d/pyobjc_framework_metal-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:de71b46062cb533be2c025cd6018fd4db9d7fd6a65bd67131d8e484c3616321a", size = 58381, upload-time = "2025-06-14T20:51:47.016Z" },
    { url = "https://files.pythonhosted.org/packages/22/f0/b7c636729ed75d05bbb236b3b813d7629ffad5fb5951710978a478ac7713/pyobjc_framework_metal-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:b4c4dcab1db5750575a49a0a903528ea64b5bb93a9f3aaac5c810117a9c07e9c", size = 58824, upload-time = "2025-06-14T20:51:47.828Z" },
    { url = "https://files.pythonhosted.org/packages/dc/22/8683231702db8a585c83db38cf9e76de2272673e7230de715ff3a868d0dc/pyobjc_framework_metal-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:432fefd3b27ab58c703b2f07afbc4690af815a9a8b4f8a997c4aefa8652e71d7", size = 59221, upload-time = "2025-06-14T20:51:48.691Z" },
]

[[package]]
name = "pyobjc-framework-metalfx"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/10/20/4c839a356b534c161fb97e06589f418fc78cc5a0808362bdecf4f9a61a8d/pyobjc_framework_metalfx-11.1.tar.gz", hash = "sha256:555c1b895d4ba31be43930f45e219a5d7bb0e531d148a78b6b75b677cc588fd8", size = 27002, upload-time = "2025-06-14T20:57:55.949Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/36/73/a8df8fa445a09fbc917a495a30b13fbcf224b5576c1e464d5ece9824a493/pyobjc_framework_metalfx-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:60e1dcdf133d2504d810c3a9ba5a02781c9d54c2112a9238de8e3ca4e8debf31", size = 10107, upload-time = "2025-06-14T20:51:51.783Z" },
    { url = "https://files.pythonhosted.org/packages/8e/7b/4d925bf5f1f0b0d254b3167999987ecafb251f589cd863bdbaf96eb4ad2a/pyobjc_framework_metalfx-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:fdced91f6b2012c556db954de0e17f6d7985d52b4af83262f4d083bcd87aa01c", size = 10122, upload-time = "2025-06-14T20:51:52.473Z" },
    { url = "https://files.pythonhosted.org/packages/0c/b3/633bbd87f9380f8e288d02b44e70845453daf640602d15c4e167536c4b45/pyobjc_framework_metalfx-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e1b2819bd6a66ba55fb7019b45d38a803ea21b8258fa41c8e9ad7c28cfe74092", size = 10284, upload-time = "2025-06-14T20:51:53.193Z" },
    { url = "https://files.pythonhosted.org/packages/03/87/2d9ac114e454575daf81a69da8e6170f0d357de3922b50e5ca5ca0968e30/pyobjc_framework_metalfx-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:aedfee1218b5784b010d618332a2cc088ba2ff9414eaa06e5db465eb5ef0aa43", size = 10315, upload-time = "2025-06-14T20:51:53.875Z" },
    { url = "https://files.pythonhosted.org/packages/69/c6/98787a080b585306101e8b56f6f0bb1c579ed8f1981e9b0362a84046ec48/pyobjc_framework_metalfx-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:934cbc969182c57f5094389fe4afe6695595757d0d61f1ab663257475fdcc593", size = 10473, upload-time = "2025-06-14T20:51:54.573Z" },
]

[[package]]
name = "pyobjc-framework-metalkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/45/cb/7e01bc61625c7a6fea9c9888c9ed35aa6bbc47cda2fcd02b6525757bc2b8/pyobjc_framework_metalkit-11.1.tar.gz", hash = "sha256:8811cd81ee9583b9330df4f2499a73dcc53f3359cb92767b409acaec9e4faa1e", size = 45135, upload-time = "2025-06-14T20:57:56.601Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/87/0c/516b6d7a67a170b7d2316701d5288797a19dd283fcc2f73b7b78973e1392/pyobjc_framework_metalkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:4854cf74fccf6ce516b49bf7cf8fc7c22da9a3743914a2f4b00f336206ad47ec", size = 8730, upload-time = "2025-06-14T20:51:57.824Z" },
    { url = "https://files.pythonhosted.org/packages/11/2a/5c55d1e57d8e90613fbce4b204b7d94a9ae7019a0928cb50cbd60bfa8191/pyobjc_framework_metalkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:62e261b7798b276fee1fee065030a5d19d173863e9c697a80d1fc9a22258ec2c", size = 8749, upload-time = "2025-06-14T20:51:58.538Z" },
    { url = "https://files.pythonhosted.org/packages/b6/e4/7b7b61d72fa235c9e364117a595c621c427217567d300da21d7417668c46/pyobjc_framework_metalkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b8a378135566e3c48838c19044e17ed2598a4050516ee1c23eee7d42439ef3c8", size = 8903, upload-time = "2025-06-14T20:51:59.392Z" },
    { url = "https://files.pythonhosted.org/packages/8a/cf/103d3233fcf2ff9ae23d5d143fde7a0d1308026ca46a35f23cffa83e6915/pyobjc_framework_metalkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ce886f3966144774d9222148eaf29fb08097d7dab5658186ded597b7c088f927", size = 8786, upload-time = "2025-06-14T20:52:01.34Z" },
    { url = "https://files.pythonhosted.org/packages/96/63/748c15b5aa70a61c6735018d55b7a22560032f2ab060ee13349ae0aaef9c/pyobjc_framework_metalkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3e0776886fcd79fe7f0c55c718ebcdf073ac3e05d03040ab284ee09902fe1c70", size = 8948, upload-time = "2025-06-14T20:52:02.081Z" },
]

[[package]]
name = "pyobjc-framework-metalperformanceshaders"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metal" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d0/11/5df398a158a6efe2c87ac5cae121ef2788242afe5d4302d703147b9fcd91/pyobjc_framework_metalperformanceshaders-11.1.tar.gz", hash = "sha256:8a312d090a0f51651e63d9001e6cc7c1aa04ceccf23b494cbf84b7fd3d122071", size = 302113, upload-time = "2025-06-14T20:57:57.407Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/df/f844516a54ef0fa1d047fe5fd94b63bc8b1218c09f7d4309b2a67a79708d/pyobjc_framework_metalperformanceshaders-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:06b2a4e446fe859e30f7efc7ccfbaefd443225a6ec53d949a113a6a4acc16c4c", size = 32888, upload-time = "2025-06-14T20:52:05.225Z" },
    { url = "https://files.pythonhosted.org/packages/b5/a2/5387ab012a20afb7252b3938a8fb5319c946a3faaa9166b79b51ab3c0bf6/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:97be4bd0ded06c663205bd1cf821e148352346f147da48dba44cf7680f0ea23b", size = 32903, upload-time = "2025-06-14T20:52:06.31Z" },
    { url = "https://files.pythonhosted.org/packages/ee/8c/5f10387b638a92ffbc3ccd04bac73c68a5119672b908b6dc90d46e30fd40/pyobjc_framework_metalperformanceshaders-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c905a3f5a34a95c1fd26bf07da505ed84b9b0a0c88a8f004914d9173f5037142", size = 33093, upload-time = "2025-06-14T20:52:07.055Z" },
    { url = "https://files.pythonhosted.org/packages/69/69/9308e2d635f1b48c373601b26a9db9df4cdbe42ad64b72d7f147b662db65/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:21ca31e4246e491df788f00978744d37db975266065f7ccbf393f027b4c6e248", size = 33012, upload-time = "2025-06-14T20:52:08.2Z" },
    { url = "https://files.pythonhosted.org/packages/2f/e6/5dfedd36c6a817afeebebe7cf748e7820df9796ca685b41b66cc09602888/pyobjc_framework_metalperformanceshaders-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c651e62ce58e75a88cfd287357fdd8d9a7f729c87248c8f43ce16025986afe6a", size = 33221, upload-time = "2025-06-14T20:52:08.976Z" },
]

[[package]]
name = "pyobjc-framework-metalperformanceshadersgraph"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-metalperformanceshaders" },
]
sdist = { url = "https://files.pythonhosted.org/packages/32/c3/8d98661f7eecd1f1b0d80a80961069081b88efd3a82fbbed2d7e6050c0ad/pyobjc_framework_metalperformanceshadersgraph-11.1.tar.gz", hash = "sha256:d25225aab4edc6f786b29fe3d9badc4f3e2d0caeab1054cd4f224258c1b6dbe2", size = 105098, upload-time = "2025-06-14T20:57:58.273Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0d/a1/2033cf8b0d9f059e3495a1d9a691751b242379c36dd5bcb96c8edb121c9e/pyobjc_framework_metalperformanceshadersgraph-11.1-py2.py3-none-any.whl", hash = "sha256:9b8b014e8301c2ae608a25f73bbf23c8f3f73a6f5fdbafddad509a21b84df681", size = 6461, upload-time = "2025-06-14T20:52:10.522Z" },
]

[[package]]
name = "pyobjc-framework-metrickit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bd/48/8ae969a51a91864000e39c1de74627b12ff587b1dbad9406f7a30dfe71f8/pyobjc_framework_metrickit-11.1.tar.gz", hash = "sha256:a79d37575489916c35840e6a07edd958be578d3be7a3d621684d028d721f0b85", size = 40952, upload-time = "2025-06-14T20:57:58.996Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/55/d1/aea4655e7eaa9ab19da8fe78ab363270443059c8a542b8f8a071b4988b57/pyobjc_framework_metrickit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a034e6b982e915da881edef87d71b063e596511d52aef7a32c683571f364156e", size = 8081, upload-time = "2025-06-14T20:52:13.72Z" },
    { url = "https://files.pythonhosted.org/packages/d9/d2/1f70e7524f6aca2e7aa7a99c4024d8c7e7cdd2ae9b338d2958548ee432c0/pyobjc_framework_metrickit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:95e98e96b8f122b0141e84f13ae9e0f91d09d0803b1c093fdc7d19123f000f9e", size = 8104, upload-time = "2025-06-14T20:52:14.405Z" },
    { url = "https://files.pythonhosted.org/packages/aa/26/d875ea9da12be79e5336e7aa9134db97eb917c968f8237235e5a70da0b72/pyobjc_framework_metrickit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:14de8dcaa107fe15546df91b1f7d51dc398169c3d1b06e02291fdb8722c6bf41", size = 8247, upload-time = "2025-06-14T20:52:15.469Z" },
    { url = "https://files.pythonhosted.org/packages/18/ae/d54e66860cb083638f0dbf8e60b71931f0357c55a7eca7c25a3198c0a561/pyobjc_framework_metrickit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:75c5a62abc535387eea6a1e1612cfa5b1d59512ebfa8a3352596d481b18cc714", size = 8150, upload-time = "2025-06-14T20:52:16.933Z" },
    { url = "https://files.pythonhosted.org/packages/ef/cf/f9c1ec5241c3ffb999b6eb026df260f0336300a13324eb53e2bf44701ec0/pyobjc_framework_metrickit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:92483af233a2c31ef73dd0f7a32988a323f9560699f2f1c6c10a8a282a7b9cfd", size = 8296, upload-time = "2025-06-14T20:52:17.646Z" },
]

[[package]]
name = "pyobjc-framework-mlcompute"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8b/e6/f064dec650fb1209f41aba0c3074416cb9b975a7cf4d05d93036e3d917f0/pyobjc_framework_mlcompute-11.1.tar.gz", hash = "sha256:f6c4c3ea6a62e4e3927abf9783c40495aa8bb9a8c89def744b0822da58c2354b", size = 89021, upload-time = "2025-06-14T20:57:59.997Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/23/cc/f47a4ac2d1a792b82206fdab58cc61b3aae15e694803ea2c81f3dfc16d9d/pyobjc_framework_mlcompute-11.1-py2.py3-none-any.whl", hash = "sha256:975150725e919f8d3d33f830898f3cd2fd19a440999faab320609487f4eae19d", size = 6778, upload-time = "2025-06-14T20:52:19.844Z" },
]

[[package]]
name = "pyobjc-framework-modelio"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a0/27/140bf75706332729de252cc4141e8c8afe16a0e9e5818b5a23155aa3473c/pyobjc_framework_modelio-11.1.tar.gz", hash = "sha256:fad0fa2c09d468ac7e49848e144f7bbce6826f2178b3120add8960a83e5bfcb7", size = 123203, upload-time = "2025-06-14T20:58:01.035Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/84/5f223b82894777388ef1aa09579d9c044044877a72075213741c97adc901/pyobjc_framework_modelio-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5d5e11389bde0852490b2a37896aaf9eb674b2a3586f2c572f9101cecb7bc576", size = 20172, upload-time = "2025-06-14T20:52:22.327Z" },
    { url = "https://files.pythonhosted.org/packages/00/8b/7c8b93d99d2102800834011f58d6e5cbb56d24c112c2e45c4730b103e4a3/pyobjc_framework_modelio-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:34fabde55d28aa8a12dd4476ad40182513cf87ee2fa928043aa6702961de302b", size = 20182, upload-time = "2025-06-14T20:52:23.063Z" },
    { url = "https://files.pythonhosted.org/packages/4d/c1/4d7830a8bd4e5b077e03e72eb8b92a336f689d5203228ecab9900d58d3c3/pyobjc_framework_modelio-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:327e1f3020001fd15bfbf4d4228581a8f64bd85872fd697b7c306343c11e25a6", size = 20408, upload-time = "2025-06-14T20:52:23.813Z" },
    { url = "https://files.pythonhosted.org/packages/a1/14/a42462624d06c87034dce4cf40ded2ca6750a4d2e393607b5fb927a773b4/pyobjc_framework_modelio-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:214a4078950bc7b86a1ea70504ecf292cccebe6515c70023efdddaaa6423f455", size = 20209, upload-time = "2025-06-14T20:52:24.541Z" },
    { url = "https://files.pythonhosted.org/packages/65/db/5c24390c08fd4f895e760cc2160137248ec0c2fa8fc12cb1bdfd93fbcfa8/pyobjc_framework_modelio-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1b1393ddb315c0e8bed3f6ce4e4b355869a30c81ff79bda3ca3a201c0fd06dad", size = 20440, upload-time = "2025-06-14T20:52:25.632Z" },
]

[[package]]
name = "pyobjc-framework-multipeerconnectivity"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/73/99/75bf6170e282d9e546b353b65af7859de8b1b27ddc431fc4afbf15423d01/pyobjc_framework_multipeerconnectivity-11.1.tar.gz", hash = "sha256:a3dacca5e6e2f1960dd2d1107d98399ff81ecf54a9852baa8ec8767dbfdbf54b", size = 26149, upload-time = "2025-06-14T20:58:01.793Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b4/fe/5c29c227f6ed81147ec6ec3e681fc680a7ffe0360f96901371435ea68570/pyobjc_framework_multipeerconnectivity-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:970031deb3dbf8da1fcb04e785d4bd2eeedae8f6677db92881df6d92b05c31d6", size = 11981, upload-time = "2025-06-14T20:52:29.406Z" },
    { url = "https://files.pythonhosted.org/packages/d3/ea/f8d928235a67feeefec80e1f679bdb0c05f94e718a9aa22b4968ad65c6d1/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c92c95ea611d5272ab37fd73bc8e68c3d8fde515a75b97d8b22dafa8acbc7daf", size = 11992, upload-time = "2025-06-14T20:52:30.148Z" },
    { url = "https://files.pythonhosted.org/packages/5a/ff/e60c8681d5c916f68fc78276d9243a91efc94a0e98717b535ce0b16e9db0/pyobjc_framework_multipeerconnectivity-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:296e10d289887cc4141c660f884cced1ec4ce64a19b3e406f13f6ce453a9425f", size = 12172, upload-time = "2025-06-14T20:52:30.857Z" },
    { url = "https://files.pythonhosted.org/packages/a9/e3/2d5cea88ac0dc4ac0b2669fa43019fcdc701463c1f08e15fc5446a6dbd2a/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:35c1a4a4b16df68b658b8531f97799995816a5bf49efd66805e3057b9bb9e474", size = 11980, upload-time = "2025-06-14T20:52:31.869Z" },
    { url = "https://files.pythonhosted.org/packages/c3/84/154fe3919bf085575e9bc7b617b31914f4f4238d1b3cf0a5c75a7bfff911/pyobjc_framework_multipeerconnectivity-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c28ad5c0c6d28cbc897aaebcc5f14798762aa9fec7f9110171570fef4d8d8a36", size = 12157, upload-time = "2025-06-14T20:52:32.567Z" },
]

[[package]]
name = "pyobjc-framework-naturallanguage"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a2/e9/5352fbf09c5d5360405dea49fb77e53ed55acd572a94ce9a0d05f64d2b70/pyobjc_framework_naturallanguage-11.1.tar.gz", hash = "sha256:ab1fc711713aa29c32719774fc623bf2d32168aed21883970d4896e901ff4b41", size = 46120, upload-time = "2025-06-14T20:58:02.808Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/f2/de86665d48737c74756b016c0f3bf93c99ca4151b48b14e2fbe7233283f8/pyobjc_framework_naturallanguage-11.1-py2.py3-none-any.whl", hash = "sha256:65a780273d2cdd12a3fa304e9c9ad822cb71facd9281f1b35a71640c53826f7c", size = 5306, upload-time = "2025-06-14T20:52:34.024Z" },
]

[[package]]
name = "pyobjc-framework-netfs"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/68/5d/d68cc59a1c1ea61f227ed58e7b185a444d560655320b53ced155076f5b78/pyobjc_framework_netfs-11.1.tar.gz", hash = "sha256:9c49f050c8171dc37e54d05dd12a63979c8b6b565c10f05092923a2250446f50", size = 15910, upload-time = "2025-06-14T20:58:03.811Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/77/cc/199b06f214f8a2db26eb47e3ab7015a306597a1bca25dcb4d14ddc65bd4a/pyobjc_framework_netfs-11.1-py2.py3-none-any.whl", hash = "sha256:f202e8e0c2e73516d3eac7a43b1c66f9911cdbb37ea32750ed197d82162c994a", size = 4143, upload-time = "2025-06-14T20:52:35.428Z" },
]

[[package]]
name = "pyobjc-framework-network"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0a/ee/5ea93e48eca341b274027e1532bd8629fd55d609cd9c39c2c3acf26158c3/pyobjc_framework_network-11.1.tar.gz", hash = "sha256:f6df7a58a1279bbc976fd7e2efe813afbbb18427df40463e6e2ee28fba07d2df", size = 124670, upload-time = "2025-06-14T20:58:05.491Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/15/c2/3c6626fdb3616fde2c173d313d15caea22d141abcc2fbf3b615f8555abe3/pyobjc_framework_network-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8cdc9be8ec3b0ae95e5c649e4bbcdf502cffd357dacc566223be707bdd5ac271", size = 19513, upload-time = "2025-06-14T20:52:38.423Z" },
    { url = "https://files.pythonhosted.org/packages/91/96/0824455bab6d321ccb5a38907ab8593e1c83b283ec850abee494278f1c96/pyobjc_framework_network-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:04582fef567392c2a10dcee9519356b79b17ab73ded050d14592da938d95b01a", size = 19537, upload-time = "2025-06-14T20:52:39.181Z" },
    { url = "https://files.pythonhosted.org/packages/5d/77/a088cfef5daf5841274b49fc57f5c5f70954c4a60b9a26160cb7beeb3e3a/pyobjc_framework_network-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:acf16738ab447a31a9f6167171b2a00d65a9370a8e84482d435b2b31c58eed94", size = 19600, upload-time = "2025-06-14T20:52:39.95Z" },
    { url = "https://files.pythonhosted.org/packages/58/af/a5a22f53f0b31c584d39ddda0d3c55f41ffdbaec95a130f86fbc2e52cd0f/pyobjc_framework_network-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cafdf953aa80934d30726baa681c1af61daf2cc9fe9e3ca582f4e3796bd0d053", size = 14769, upload-time = "2025-06-14T20:52:40.678Z" },
    { url = "https://files.pythonhosted.org/packages/e6/cf/3cbbc1213caa45171fb2c8890a91302cee452283cc0be8b06aca35e2b1ad/pyobjc_framework_network-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2e45d8fdc0ad553cc35839cae5eab221fe5f7ce28758d693b8159e619ea06eac", size = 14832, upload-time = "2025-06-14T20:52:41.454Z" },
]

[[package]]
name = "pyobjc-framework-networkextension"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/71/30/d1eee738d702bbca78effdaa346a2b05359ab8a96d961b7cb44838e236ca/pyobjc_framework_networkextension-11.1.tar.gz", hash = "sha256:2b74b430ca651293e5aa90a1e7571b200d0acbf42803af87306ac8a1c70b0d4b", size = 217252, upload-time = "2025-06-14T20:58:06.311Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b6/26/526cd9f63e390e9c2153c41dc0982231b0b1ca88865deb538b77e1c3513d/pyobjc_framework_networkextension-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:853458aae8b43634461f6c44759750e2dc784c9aba561f9468ab14529b5a7fbe", size = 14114, upload-time = "2025-06-14T20:52:45.274Z" },
    { url = "https://files.pythonhosted.org/packages/06/30/ab050541fda285e2ce6b6ba0f1f5215809bd5ec75f71de8057ff8135737a/pyobjc_framework_networkextension-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:d3d6e9810cb01c3a8f99aed5ee2d75f6f785204338b99b32e5f64370a18cc9dd", size = 14128, upload-time = "2025-06-14T20:52:46.328Z" },
    { url = "https://files.pythonhosted.org/packages/07/36/3980a3ee5fe4be7c442cb4ddcf03f63406055da3f5ad58640fb573ecd77c/pyobjc_framework_networkextension-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7dea914e7b26e28c6e4f8ffd03dd8fce612d38876043944fb0cf191774634566", size = 14275, upload-time = "2025-06-14T20:52:47.019Z" },
    { url = "https://files.pythonhosted.org/packages/42/48/732767e8f858bd35fafce7ef846444569fb239e08d598e394c429c8bb78e/pyobjc_framework_networkextension-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:4c9d6c08b8f1cf374351bcecf8bbc91e6a8999b84d52f30964f4f1e6a323943c", size = 14179, upload-time = "2025-06-14T20:52:48.126Z" },
    { url = "https://files.pythonhosted.org/packages/c8/02/9b2493f6894c873c751e097b692744ce0360248ff1b55dd64ff3716877d6/pyobjc_framework_networkextension-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:6d730540d97662867f3cfd90c9a1e69a6adae0f5eb554c1b94a1b067e7ebc728", size = 14323, upload-time = "2025-06-14T20:52:48.851Z" },
]

[[package]]
name = "pyobjc-framework-notificationcenter"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a8/4a/d3529b9bd7aae2c89d258ebc234673c5435e217a5136abd8c0aba37b916b/pyobjc_framework_notificationcenter-11.1.tar.gz", hash = "sha256:0b938053f2d6b1cea9db79313639d7eb9ddd5b2a5436a346be0887e75101e717", size = 23389, upload-time = "2025-06-14T20:58:07.136Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6d/92/cd00fe5e54a191fb77611fe728a8c8a0a6edb229857d32f27806582406ca/pyobjc_framework_notificationcenter-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:65fc67374a471890245c7a1d60cf67dcf160075a9c048a5d89608a8290f33b03", size = 9880, upload-time = "2025-06-14T20:52:52.406Z" },
    { url = "https://files.pythonhosted.org/packages/40/e4/1bc444c5ee828a042e951c264ce597207e192fb6701c380db5ba05486955/pyobjc_framework_notificationcenter-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f5ce98882e301adef07651ba495ddd57b661d4c0398afd39f4591c1b44673cca", size = 9895, upload-time = "2025-06-14T20:52:53.105Z" },
    { url = "https://files.pythonhosted.org/packages/13/b9/b98d74bcc9e1694494b81dd1bfeb28e2f004041db4945b7451c0c6c64b1e/pyobjc_framework_notificationcenter-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e46285290d04e84c167606ccfcb9a20c2567f5a2a6a9c6e96760fc9d561c2740", size = 10090, upload-time = "2025-06-14T20:52:53.814Z" },
    { url = "https://files.pythonhosted.org/packages/4b/1e/3d6b9765f3f2719733b099cb48750366d9bbd431a1b5b0e6dd30ece7a995/pyobjc_framework_notificationcenter-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:c3e79e9c57f130099b47bde48f26fcd90ab3b52e01d989ea15b7cdb7fa5a34d8", size = 9935, upload-time = "2025-06-14T20:52:54.589Z" },
    { url = "https://files.pythonhosted.org/packages/f3/13/1a85878f14232d8b7012a5a24dbf185dec1864dc92ca53db4c62390b6ee5/pyobjc_framework_notificationcenter-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:15e49491d7f091eaa643f2fd89787becbf767dd6c609aa3d01e53132cb1d9fa1", size = 10137, upload-time = "2025-06-14T20:52:55.312Z" },
]

[[package]]
name = "pyobjc-framework-opendirectory"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9d/02/ac56c56fdfbc24cdf87f4a624f81bbe2e371d0983529b211a18c6170e932/pyobjc_framework_opendirectory-11.1.tar.gz", hash = "sha256:319ac3424ed0350be458b78148914468a8fc13a069d62e7869e3079108e4f118", size = 188880, upload-time = "2025-06-14T20:58:08.003Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/06/56/f0f5b7222d5030192c44010ab7260681e349efea2f1b1b9f116ba1951d6d/pyobjc_framework_opendirectory-11.1-py2.py3-none-any.whl", hash = "sha256:bb4219b0d98dff4a952c50a79b1855ce74e1defd0d241f3013def5b09256fd7b", size = 11829, upload-time = "2025-06-14T20:52:56.715Z" },
]

[[package]]
name = "pyobjc-framework-osakit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/56/22/f9cdfb5de255b335f99e61a3284be7cb1552a43ed1dfe7c22cc868c23819/pyobjc_framework_osakit-11.1.tar.gz", hash = "sha256:920987da78b67578367c315d208f87e8fab01dd35825d72242909f29fb43c820", size = 22290, upload-time = "2025-06-14T20:58:09.103Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/14/65/c6531ce0792d5035d87f054b0ccf22e453328fda2e68e11a7f70486da23a/pyobjc_framework_osakit-11.1-py2.py3-none-any.whl", hash = "sha256:1b0c0cc537ffb8a8365ef9a8b46f717a7cc2906414b6a3983777a6c0e4d53d5a", size = 4143, upload-time = "2025-06-14T20:52:57.555Z" },
]

[[package]]
name = "pyobjc-framework-oslog"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/93/3feb7f6150b50165524750a424f5434448392123420cb4673db766c3f54a/pyobjc_framework_oslog-11.1.tar.gz", hash = "sha256:b2af409617e6b68fa1f1467c5a5679ebf59afd0cdc4b4528e1616059959a7979", size = 24689, upload-time = "2025-06-14T20:58:09.739Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/40/da/fd3bd62899cd679743056aa2c28bc821c2688682a17ddde1a08d6d9d67fc/pyobjc_framework_oslog-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7ae29c31ce51c476d3a37ca303465dd8bdfa98df2f6f951cf14c497e984a1ba9", size = 7799, upload-time = "2025-06-14T20:52:59.935Z" },
    { url = "https://files.pythonhosted.org/packages/9d/a9/d26bb3ec7ab2a3ef843c1697b6084dbd4a4a98d90ff8e29f4c227ade425e/pyobjc_framework_oslog-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7174ca2cdc073e555d5f5aea3baa7410c61a83a3741eaec23e8581340037680e", size = 7811, upload-time = "2025-06-14T20:53:00.621Z" },
    { url = "https://files.pythonhosted.org/packages/44/60/2f57ee052e9df2700b21032774146ae622af0a88a8dff97158dc5850a0ec/pyobjc_framework_oslog-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f03789f8d5638e1075652b331b8ebf98c03dfa809c57545f0313583a7688bb86", size = 7995, upload-time = "2025-06-14T20:53:01.316Z" },
    { url = "https://files.pythonhosted.org/packages/2f/f1/13fe8d1cebe29953e8754d9118399805b266e17ef885f628f62f2d2deb9b/pyobjc_framework_oslog-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a302272aa40d1655be635e0f0dd0ca71b5fce562dfcb88a87165a170a648b2fd", size = 7847, upload-time = "2025-06-14T20:53:02.032Z" },
    { url = "https://files.pythonhosted.org/packages/37/82/a5a2fb3333c3f55ba696baee67668e44380b9838dd91b64a038ed57cee41/pyobjc_framework_oslog-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:cade8869e185a29fb88fc48e2e5c984548433f669c1a40ec7f5640994fa36603", size = 8034, upload-time = "2025-06-14T20:53:02.72Z" },
]

[[package]]
name = "pyobjc-framework-passkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5c/05/063db500e7df70e39cbb5518a5a03c2acc06a1ca90b057061daea00129f3/pyobjc_framework_passkit-11.1.tar.gz", hash = "sha256:d2408b58960fca66607b483353c1ffbd751ef0bef394a1853ec414a34029566f", size = 144859, upload-time = "2025-06-14T20:58:10.761Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9d/ba/9e52213e0c0100079e4ef397cf4fd5ba8939fa4de19339755d1a373407a8/pyobjc_framework_passkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:779eaea4e1931cfda4c8701e1111307b14bf9067b359a319fc992b6848a86932", size = 13959, upload-time = "2025-06-14T20:53:05.694Z" },
    { url = "https://files.pythonhosted.org/packages/d1/4f/e29dc665382e22cd6b4ebb1c5707a1b2059018a6462c81a7c344a9c40dba/pyobjc_framework_passkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a6306dda724ca812dca70154d40f32ec9bbdaff765a12f3cc45391723efe147e", size = 13971, upload-time = "2025-06-14T20:53:06.413Z" },
    { url = "https://files.pythonhosted.org/packages/f4/ec/ef03f62924b288302e41373c4c292cadf4c393519828a9986d8573b72bcc/pyobjc_framework_passkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d7948d5b3369b60808a85dcadffdebb0a44e8d2c4716edc10b78cb76fa762070", size = 14130, upload-time = "2025-06-14T20:53:07.169Z" },
    { url = "https://files.pythonhosted.org/packages/92/cb/4ecaf64825de3589cbf5119cf6bfabe7b466faff58357800255c2ecf41e1/pyobjc_framework_passkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:bfff2a63850afe702ba25f661360393389ffb58e127d47488c414caa9e676aa7", size = 14010, upload-time = "2025-06-14T20:53:08.254Z" },
    { url = "https://files.pythonhosted.org/packages/ce/72/125088bd20a8f771cc1749c6be786241839c6bdb6a581cf025663f55fa1f/pyobjc_framework_passkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f6b7f3cd7c6855af1b6fc4036ae2f10779a312182107c94d36ef63c2dd4a6f87", size = 14180, upload-time = "2025-06-14T20:53:08.972Z" },
]

[[package]]
name = "pyobjc-framework-pencilkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/75/d0/bbbe9dadcfc37e33a63d43b381a8d9a64eca27559df38efb74d524fa6260/pyobjc_framework_pencilkit-11.1.tar.gz", hash = "sha256:9c173e0fe70179feadc3558de113a8baad61b584fe70789b263af202bfa4c6be", size = 22570, upload-time = "2025-06-14T20:58:11.538Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a3/f6/59ffc3f26ea9cfda4d40409f9afc2a38e5c0c6a68a3a8c9202e8b98b03b1/pyobjc_framework_pencilkit-11.1-py2.py3-none-any.whl", hash = "sha256:b7824907bbcf28812f588dda730e78f662313baf40befd485c6f2fcb49018019", size = 4026, upload-time = "2025-06-14T20:53:10.449Z" },
]

[[package]]
name = "pyobjc-framework-phase"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-avfoundation" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c6/d2/e9384b5b3fbcc79e8176cb39fcdd48b77f60cd1cb64f9ee4353762b037dc/pyobjc_framework_phase-11.1.tar.gz", hash = "sha256:a940d81ac5c393ae3da94144cf40af33932e0a9731244e2cfd5c9c8eb851e3fc", size = 58986, upload-time = "2025-06-14T20:58:12.196Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f5/9e/55782f02b3bfb58f030b062176e8b0dba5f8fbd6e50d27a687f559c4179d/pyobjc_framework_phase-11.1-py2.py3-none-any.whl", hash = "sha256:cfa61f9c6c004161913946501538258aed48c448b886adbf9ed035957d93fa15", size = 6822, upload-time = "2025-06-14T20:53:11.618Z" },
]

[[package]]
name = "pyobjc-framework-photos"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/78/b0/576652ecd05c26026ab4e75e0d81466edd570d060ce7df3d6bd812eb90d0/pyobjc_framework_photos-11.1.tar.gz", hash = "sha256:c8c3b25b14a2305047f72c7c081ff3655b3d051f7ed531476c03246798f8156d", size = 92569, upload-time = "2025-06-14T20:58:12.939Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fa/24/2400e6b738d3ed622c61a7cc6604eec769f398071a1eb6a16dfdf3a9ceea/pyobjc_framework_photos-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8dbfffd29cfa63a8396ede0030785c15a5bc36065d3dd98fc6176a59e7abb3d3", size = 12224, upload-time = "2025-06-14T20:53:14.793Z" },
    { url = "https://files.pythonhosted.org/packages/70/60/cc575ee4287b250a42406e9b335f3293840996a840152cf93d1ce73790c5/pyobjc_framework_photos-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:541d8fafdb2f111f2f298e1aa0542f2d5871ce1dd481c3e9be4ed33916b38c3a", size = 12241, upload-time = "2025-06-14T20:53:15.469Z" },
    { url = "https://files.pythonhosted.org/packages/8c/3b/d9c4c5b156e7805495a8864dd06a3439c3b4267e5887d9094ac45a4ca907/pyobjc_framework_photos-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7cded282eaebd77645a4262f6fb63379c7a226d20f8f1763910b19927709aea2", size = 12426, upload-time = "2025-06-14T20:53:16.207Z" },
    { url = "https://files.pythonhosted.org/packages/28/86/06d9e61aa5c6114cca5ae77e3c037f371943e9110aab4ce6d31d19ffb669/pyobjc_framework_photos-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:3a759ebcf46493cd09e5c89c0a09096ad83ae837d9236e437571bb22ca6eab3f", size = 12290, upload-time = "2025-06-14T20:53:16.897Z" },
    { url = "https://files.pythonhosted.org/packages/69/07/849ca5aefc646b92ea399073f90628215198701a59c1b62b7bf3e27bbbdf/pyobjc_framework_photos-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:72e0ed9bc5f1890f882df55333797da95c0ed1c1d7a0fe7d869a8d4ee4e1bdfd", size = 12470, upload-time = "2025-06-14T20:53:17.592Z" },
]

[[package]]
name = "pyobjc-framework-photosui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/bb/e6de720efde2e9718677c95c6ae3f97047be437cda7a0f050cd1d6d2a434/pyobjc_framework_photosui-11.1.tar.gz", hash = "sha256:1c7ffab4860ce3e2b50feeed4f1d84488a9e38546db0bec09484d8d141c650df", size = 48443, upload-time = "2025-06-14T20:58:13.626Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f8/c1/a5c84c1695e7a066743d63d10b219d94f3c07d706871682e42f7db389f5c/pyobjc_framework_photosui-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b2f278f569dfd596a32468351411518a651d12cb91e60620094e852c525a5f10", size = 11682, upload-time = "2025-06-14T20:53:21.162Z" },
    { url = "https://files.pythonhosted.org/packages/33/10/506af430a9e7d356302b6bbee6672e03a4dfbc9a2f3a90fa79607d06387d/pyobjc_framework_photosui-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6f0fa9c9e363c0db54957dfe4e26214379f2698caaba1e4ff4c9e3eba5e690d9", size = 11697, upload-time = "2025-06-14T20:53:21.855Z" },
    { url = "https://files.pythonhosted.org/packages/9f/f8/ada0d54136f14b071e784e7f86e0a1e2190e2e898a7f4172b53e1fec5f7c/pyobjc_framework_photosui-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:91aff7caae16a7a7f25e35692aa92b796155510b8a0575668e75f351fbf63a68", size = 11894, upload-time = "2025-06-14T20:53:22.536Z" },
    { url = "https://files.pythonhosted.org/packages/1b/7d/b55a787f90e29f36b776cf87b9515a53014449d9cddd109b9e81c9e9d7eb/pyobjc_framework_photosui-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e607242e09fb7d4bcad2f3eb2e88529d8f2ff7cf7341cd2c6c5b3f4d6744218e", size = 11670, upload-time = "2025-06-14T20:53:23.22Z" },
    { url = "https://files.pythonhosted.org/packages/07/be/3e98e69e513b3948080ede2a13b0f73f081db50c716519fcee4a932de0b6/pyobjc_framework_photosui-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f11f6043c83b2c65ecad69c48844fff6368127af3956ec8df9726bbd1e5da17e", size = 11891, upload-time = "2025-06-14T20:53:23.901Z" },
]

[[package]]
name = "pyobjc-framework-preferencepanes"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/34/ac/9324602daf9916308ebf1935b8a4b91c93b9ae993dcd0da731c0619c2836/pyobjc_framework_preferencepanes-11.1.tar.gz", hash = "sha256:6e4a55195ec9fc921e0eaad6b3038d0ab91f0bb2f39206aa6fccd24b14a0f1d8", size = 26212, upload-time = "2025-06-14T20:58:14.361Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a1/51/75c7e32272241f706ce8168e04a32be02c4b0c244358330f730fc85695c3/pyobjc_framework_preferencepanes-11.1-py2.py3-none-any.whl", hash = "sha256:6ee5f5a7eb294e03ea3bac522ac4b69e6dc83ceceff627a0a2d289afe1e01ad9", size = 4786, upload-time = "2025-06-14T20:53:25.603Z" },
]

[[package]]
name = "pyobjc-framework-pushkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9f/f0/92d0eb26bf8af8ebf6b5b88df77e70b807de11f01af0162e0a429fcfb892/pyobjc_framework_pushkit-11.1.tar.gz", hash = "sha256:540769a4aadc3c9f08beca8496fe305372501eb28fdbca078db904a07b8e10f4", size = 21362, upload-time = "2025-06-14T20:58:15.642Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/31/65/260014c5d13c54bd359221b0a890cbffdb99eecff3703f253cf648e45036/pyobjc_framework_pushkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:21993b7e9127b05575a954faa68e85301c6a4c04e34e38aff9050f67a05c562a", size = 8174, upload-time = "2025-06-14T20:53:28.805Z" },
    { url = "https://files.pythonhosted.org/packages/b4/b2/08514fa6be83a359bb6d72f9009f17f16f7efc0fe802029d1f6f0c4fc5c9/pyobjc_framework_pushkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:bac3ee77dfbe936998f207c1579e346993485bab8849db537ed250261cf12ab3", size = 8190, upload-time = "2025-06-14T20:53:29.651Z" },
    { url = "https://files.pythonhosted.org/packages/46/d0/cbe99c9bf3b9fb2679c08f4051aaa44dcfbfa9e762f0ef4c7fc5ad2e147e/pyobjc_framework_pushkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:68c4f44354eab84cb54d43310fa65ca3a5ba68299c868378764cc50803cf2adc", size = 8314, upload-time = "2025-06-14T20:53:31.178Z" },
    { url = "https://files.pythonhosted.org/packages/87/ff/7b0747471b837580dc01709438a5a0949ce909957d2857408bd81bf22155/pyobjc_framework_pushkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:cfec36cdca24654be0465282eb31b7ff3674ea4b7f3ce696b07edbe33b000aa5", size = 8240, upload-time = "2025-06-14T20:53:31.852Z" },
    { url = "https://files.pythonhosted.org/packages/86/96/422875f53390579dd51d1cdc696290c5693d293e9c4cb0f6d4e7a0905f88/pyobjc_framework_pushkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80d5d8240b71631d81cfa96f398fae1d137be98f224739e50edaf9e5afc21a9d", size = 8368, upload-time = "2025-06-14T20:53:32.53Z" },
]

[[package]]
name = "pyobjc-framework-quartz"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c7/ac/6308fec6c9ffeda9942fef72724f4094c6df4933560f512e63eac37ebd30/pyobjc_framework_quartz-11.1.tar.gz", hash = "sha256:a57f35ccfc22ad48c87c5932818e583777ff7276605fef6afad0ac0741169f75", size = 3953275, upload-time = "2025-06-14T20:58:17.924Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9b/37/ee6e0bdd31b3b277fec00e5ee84d30eb1b5b8b0e025095e24ddc561697d0/pyobjc_framework_quartz-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9ac806067541917d6119b98d90390a6944e7d9bd737f5c0a79884202327c9204", size = 216410, upload-time = "2025-06-14T20:53:36.346Z" },
    { url = "https://files.pythonhosted.org/packages/bd/27/4f4fc0e6a0652318c2844608dd7c41e49ba6006ee5fb60c7ae417c338357/pyobjc_framework_quartz-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:43a1138280571bbf44df27a7eef519184b5c4183a588598ebaaeb887b9e73e76", size = 216816, upload-time = "2025-06-14T20:53:37.358Z" },
    { url = "https://files.pythonhosted.org/packages/b8/8a/1d15e42496bef31246f7401aad1ebf0f9e11566ce0de41c18431715aafbc/pyobjc_framework_quartz-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b23d81c30c564adf6336e00b357f355b35aad10075dd7e837cfd52a9912863e5", size = 221941, upload-time = "2025-06-14T20:53:38.34Z" },
    { url = "https://files.pythonhosted.org/packages/32/a8/a3f84d06e567efc12c104799c7fd015f9bea272a75f799eda8b79e8163c6/pyobjc_framework_quartz-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:07cbda78b4a8fcf3a2d96e047a2ff01f44e3e1820f46f0f4b3b6d77ff6ece07c", size = 221312, upload-time = "2025-06-14T20:53:39.435Z" },
    { url = "https://files.pythonhosted.org/packages/76/ef/8c08d4f255bb3efe8806609d1f0b1ddd29684ab0f9ffb5e26d3ad7957b29/pyobjc_framework_quartz-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:39d02a3df4b5e3eee1e0da0fb150259476910d2a9aa638ab94153c24317a9561", size = 226353, upload-time = "2025-06-14T20:53:40.655Z" },
]

[[package]]
name = "pyobjc-framework-quicklookthumbnailing"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/aa/98/6e87f360c2dfc870ae7870b8a25fdea8ddf1d62092c755686cebe7ec1a07/pyobjc_framework_quicklookthumbnailing-11.1.tar.gz", hash = "sha256:1614dc108c1d45bbf899ea84b8691288a5b1d25f2d6f0c57dfffa962b7a478c3", size = 16527, upload-time = "2025-06-14T20:58:20.811Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/65/4a/ddc35bdcd44278f22df2154a52025915dba6c80d94e458d92e9e7430d1e4/pyobjc_framework_quicklookthumbnailing-11.1-py2.py3-none-any.whl", hash = "sha256:4d1863c6c83c2a199c1dbe704b4f8b71287168f4090ed218d37dc59277f0d9c9", size = 4219, upload-time = "2025-06-14T20:53:43.198Z" },
]

[[package]]
name = "pyobjc-framework-replaykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c8/4f/014e95f0fd6842d7fcc3d443feb6ee65ac69d06c66ffa9327fc33ceb7c27/pyobjc_framework_replaykit-11.1.tar.gz", hash = "sha256:6919baa123a6d8aad769769fcff87369e13ee7bae11b955a8185a406a651061b", size = 26132, upload-time = "2025-06-14T20:58:21.853Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/73/846cebb36fc279df18f10dc3a27cba8fe2e47e95350a3651147e4d454719/pyobjc_framework_replaykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:22c6d09be9a6e758426d723a6c3658ad6bbb66f97ba9a1909bfcf29a91d99921", size = 10087, upload-time = "2025-06-14T20:53:46.242Z" },
    { url = "https://files.pythonhosted.org/packages/bf/2e/996764cd045b6c9e033167e573c9fe67c4e867eb6ab49c2d4fde005cd4a7/pyobjc_framework_replaykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7742ee18c8c9b61f5668698a05b88d25d34461fcdd95a8f669ecdfd8db8c4d42", size = 10108, upload-time = "2025-06-14T20:53:47.293Z" },
    { url = "https://files.pythonhosted.org/packages/d6/f9/1013a88f655b9eaf6fc81a5da48403724435cf2f87c147038dfa733e6213/pyobjc_framework_replaykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b503fabc33ee02117fd82c78db18cba3f0be90dea652f5553101a45185100402", size = 10298, upload-time = "2025-06-14T20:53:47.992Z" },
    { url = "https://files.pythonhosted.org/packages/fc/df/62a735c034bdbd0670f93636725b898a762fd23532a3841ae491bc8d16bd/pyobjc_framework_replaykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:da84e48ba5d529ae72b975f0d81c5bd5427983c2b05d3d2c7fd54a6cbdf0d0f9", size = 10170, upload-time = "2025-06-14T20:53:48.682Z" },
    { url = "https://files.pythonhosted.org/packages/56/00/d582fd058e580e5f803ee57fa8513b7df0c6d2abca876e04a4bc682b7143/pyobjc_framework_replaykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2bf2180feae500fdd6f14360200fda0b6650a4ec39fe5d84a5dde9e8cdd307b6", size = 10347, upload-time = "2025-06-14T20:53:49.383Z" },
]

[[package]]
name = "pyobjc-framework-safariservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1a/fc/c47d2abf3c1de6db21d685cace76a0931d594aa369e3d090260295273f6e/pyobjc_framework_safariservices-11.1.tar.gz", hash = "sha256:39a17df1a8e1c339457f3acbff0dc0eae4681d158f9d783a11995cf484aa9cd0", size = 34905, upload-time = "2025-06-14T20:58:22.492Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d7/13/9636e9d3dc362daaaa025b2aa4e28606a1e197dfc6506d3a246be8315f8a/pyobjc_framework_safariservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c92eb9e35f98368ea1bfaa8cdd41138ca8b004ea5a85833390a44e5626ca5061", size = 7275, upload-time = "2025-06-14T20:53:53.075Z" },
    { url = "https://files.pythonhosted.org/packages/de/cd/9ed0083373be3bf6da2450a6800b54965fea95b2452473ee0e36ddc72573/pyobjc_framework_safariservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8b4d4169dd21e69246d90a42f872b7148064b63de6bbbf6bc6ddabe33f143843", size = 7290, upload-time = "2025-06-14T20:53:53.816Z" },
    { url = "https://files.pythonhosted.org/packages/42/ed/3eaec77c81395410441466f66c8920664ba72f62099306f0e9b878b0b203/pyobjc_framework_safariservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8a4371d64052a3ffe9993a89c45f9731f86e7b6c21fd1d968815fd7930ff501a", size = 7293, upload-time = "2025-06-14T20:53:54.508Z" },
    { url = "https://files.pythonhosted.org/packages/d2/5f/5bbdf64ec7ff2c1d90e0b7b7186a55981632c16ce757b3187e87d6707c7e/pyobjc_framework_safariservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:abdbe0d8a79caa994a1d2be8ea4e5a1e4c80f7d8e1f0750f9c365129d1f1a968", size = 7312, upload-time = "2025-06-14T20:53:55.193Z" },
    { url = "https://files.pythonhosted.org/packages/fd/2a/dd6d53915c83c1e68bd8cfdec5cf71c4b3c6e1b7c737353f109b2dde5426/pyobjc_framework_safariservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:8a6ec417d35a0600629eba97c0ab2f2d09fae171e8bca3d3d6aa1c7ff272c4d7", size = 7318, upload-time = "2025-06-14T20:53:55.875Z" },
]

[[package]]
name = "pyobjc-framework-safetykit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/28/cc/f6aa5d6f45179bd084416511be4e5b0dd0752cb76daa93869e6edb806096/pyobjc_framework_safetykit-11.1.tar.gz", hash = "sha256:c6b44e0cf69e27584ac3ef3d8b771d19a7c2ccd9c6de4138d091358e036322d4", size = 21240, upload-time = "2025-06-14T20:58:23.132Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9c/8f/6f4c833e31526a81faef9bf19695b332ba8d2fa53d92640abd6fb3ac1d78/pyobjc_framework_safetykit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b76fccdb970d3d751a540c47712e9110afac9abea952cb9b7bc0d5867db896e3", size = 8523, upload-time = "2025-06-14T20:53:59.443Z" },
    { url = "https://files.pythonhosted.org/packages/85/3d/782e1738f2eb4b276baabd85a8b263bf75b2c4e990fd5950eeadfb59ebeb/pyobjc_framework_safetykit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8130de57f701dbccb1d84c76ec007fe04992da58cbf0eb906324393eeac3d08d", size = 8541, upload-time = "2025-06-14T20:54:00.461Z" },
    { url = "https://files.pythonhosted.org/packages/be/2c/411d525a2110777dd22888e46a48dcff2ae15ff08ab2f739eab44ee740cb/pyobjc_framework_safetykit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:cd8091c902037eac4a403d8462424afd711f43206af3548a34bebe1f59d2c340", size = 8701, upload-time = "2025-06-14T20:54:01.156Z" },
    { url = "https://files.pythonhosted.org/packages/ca/df/f04b5caa76b2e4c5115c55937b50c341963c35ded6931cb1a3bc0e686d0b/pyobjc_framework_safetykit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:761304365978d650015fe05fb624ba13ea4af6c6a76ef8e344673f5b0fed2e92", size = 8581, upload-time = "2025-06-14T20:54:01.838Z" },
    { url = "https://files.pythonhosted.org/packages/a5/66/e0bd5ac4956e4f6d77815c85355764e43934a31c8fdd10e33b4ff217cb99/pyobjc_framework_safetykit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:24d5ce9dfb80abb634a95ceda3da0f0cdb52c765db0f47de953a4f66b918c957", size = 8746, upload-time = "2025-06-14T20:54:02.534Z" },
]

[[package]]
name = "pyobjc-framework-scenekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/64/cf/2d89777120d2812e7ee53c703bf6fc8968606c29ddc1351bc63f0a2a5692/pyobjc_framework_scenekit-11.1.tar.gz", hash = "sha256:82941f1e5040114d6e2c9fd35507244e102ef561c637686091b71a7ad0f31306", size = 214118, upload-time = "2025-06-14T20:58:24.003Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e0/f9/bdcd8a4bc6c387ef07f3e2190cea6a03d4f7ed761784f492b01323e8d900/pyobjc_framework_scenekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c803d95b30c4ce49f46ff7174806f5eb84e4c3a152f8f580c5da0313c5c67041", size = 33558, upload-time = "2025-06-14T20:54:05.59Z" },
    { url = "https://files.pythonhosted.org/packages/ce/5e/9bb308fd68b56a8cf9ea5213e6c988232ce6ae4e6ccd4cf53b38f0018deb/pyobjc_framework_scenekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2f347d5ae42af8acddb86a45f965046bb91f8d83d33851390954439961e2a7b7", size = 33577, upload-time = "2025-06-14T20:54:06.69Z" },
    { url = "https://files.pythonhosted.org/packages/e0/96/c960c553de8e70f0bff275e19295b6254127f3f6d1da4e5dd80fd7037d49/pyobjc_framework_scenekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ea2f02eea982872994d7c366f6a51060a90cc17b994c017f85c094e2bc346847", size = 33912, upload-time = "2025-06-14T20:54:07.456Z" },
    { url = "https://files.pythonhosted.org/packages/04/29/c342990cc245a3bdbb9d55807ce8009575acb705dbce24164001850ec41e/pyobjc_framework_scenekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:2be143172b43c2cf4a2b3fad9e15ffb5d29df677d3678160cd125b94a30caaca", size = 34061, upload-time = "2025-06-14T20:54:08.571Z" },
    { url = "https://files.pythonhosted.org/packages/25/aa/eff356d201d32b1f7e2a2e8c6629899cb31bcc33933816055ce1b90df31a/pyobjc_framework_scenekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:3f62f2b8f26375ecfec71f7fdb23f2739cf93d213968c6ffac6a8525516ffc6e", size = 34365, upload-time = "2025-06-14T20:54:09.329Z" },
]

[[package]]
name = "pyobjc-framework-screencapturekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
]
sdist = { url = "https://files.pythonhosted.org/packages/32/a5/9bd1f1ad1773a1304ccde934ff39e0f0a0b0034441bf89166aea649606de/pyobjc_framework_screencapturekit-11.1.tar.gz", hash = "sha256:11443781a30ed446f2d892c9e6642ca4897eb45f1a1411136ca584997fa739e0", size = 53548, upload-time = "2025-06-14T20:58:24.837Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/98/37/840f306dcf01dd2bd092ae8dcf371a3bad3a0f88f0780d0840f899a8c047/pyobjc_framework_screencapturekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:641fa7834f54558859209e174c83551d5fa239ca6943ace52665f7d45e562ff2", size = 11308, upload-time = "2025-06-14T20:54:12.382Z" },
    { url = "https://files.pythonhosted.org/packages/1b/9e/de4c2e3ae834c2f60c9e78d95e1f2488b679b4cf74fa5bfba7f065fb827b/pyobjc_framework_screencapturekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1119d6258d6c668564ab39154cfc745fd2bb8b3beeaa4f9b2a8a4c93926678c0", size = 11324, upload-time = "2025-06-14T20:54:13.104Z" },
    { url = "https://files.pythonhosted.org/packages/4c/49/fa1680b8453fb5c4bbe92b2bfef145fd90b3cd9c2ee24c1eb786b7655cd3/pyobjc_framework_screencapturekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f93f8198741bd904d423a7b1ef941445246bdf6cb119597d981e61a13cc479a4", size = 11517, upload-time = "2025-06-14T20:54:13.829Z" },
    { url = "https://files.pythonhosted.org/packages/12/cd/035192d486f4323d0d891b50fd2229a58e80fd341e19fa7ae9d71c38c8e2/pyobjc_framework_screencapturekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:9e135b414d3829fcf7fd8a66c94e8b51135fb9f630c10488fb9d78f27f622906", size = 11396, upload-time = "2025-06-14T20:54:14.881Z" },
    { url = "https://files.pythonhosted.org/packages/a3/4a/e2752b1d91ce420ccd58a24e5e819230007fa50e97719a78857a76f8ab6d/pyobjc_framework_screencapturekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:9972db69064b69e78fbc6a00f1de2d8eaa225b990b23687970328b061e60e26d", size = 11578, upload-time = "2025-06-14T20:54:15.562Z" },
]

[[package]]
name = "pyobjc-framework-screensaver"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/7c/f6/f2d48583b29fc67b64aa1f415fd51faf003d045cdb1f3acab039b9a3f59f/pyobjc_framework_screensaver-11.1.tar.gz", hash = "sha256:d5fbc9dc076cc574ead183d521840b56be0c160415e43cb8e01cfddd6d6372c2", size = 24302, upload-time = "2025-06-14T20:58:25.52Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/f9/4ae982c7a1387b64954130b72187e140329b73c647acb4d6b6eb3c033d8d/pyobjc_framework_screensaver-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f2d22293cf9d715e4692267a1678096afd6793c0519d9417cf77c8a6c706a543", size = 8402, upload-time = "2025-06-14T20:54:19.044Z" },
    { url = "https://files.pythonhosted.org/packages/dc/ff/c2e83551474d3c401181ce1d859ebd0e0b1986ab8ee932d647debebbe7eb/pyobjc_framework_screensaver-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:46d65c1e14d35f287e7be351e2f98daf9489e31e7ca0d306e6102904ce6c40fb", size = 8419, upload-time = "2025-06-14T20:54:19.741Z" },
    { url = "https://files.pythonhosted.org/packages/7a/b7/e633cd8e07bcfcd675155c7fd00f82cab0d09ca3edee0f568bcfc0ae8ea4/pyobjc_framework_screensaver-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:2c01a9646bc118445cbb117e7016bd1df9fe93a65db991ab5496d59b1a7bc66d", size = 8423, upload-time = "2025-06-14T20:54:20.447Z" },
    { url = "https://files.pythonhosted.org/packages/65/55/ac2b76a86646b6f86163d1e06c2ca36f4b0fb168ae889ab3af657b724817/pyobjc_framework_screensaver-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e32c83e1d9e5044d482916ac42257a87d1f1068f3f6bccaa04edda40fb9f9ad1", size = 8457, upload-time = "2025-06-14T20:54:21.131Z" },
    { url = "https://files.pythonhosted.org/packages/d5/e7/494e6aa650c071abd3b44a0168123a174636a1fc9d198f0db80d642703cc/pyobjc_framework_screensaver-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:7852c2281148cb99c87c4c25b83dca7fdd11e6eed04deadcf2201ed5a2079e5f", size = 8462, upload-time = "2025-06-14T20:54:21.949Z" },
]

[[package]]
name = "pyobjc-framework-screentime"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/82/33/ebed70a1de134de936bb9a12d5c76f24e1e335ff4964f9bb0af9b09607f1/pyobjc_framework_screentime-11.1.tar.gz", hash = "sha256:9bb8269456bbb674e1421182efe49f9168ceefd4e7c497047c7bf63e2f510a34", size = 14875, upload-time = "2025-06-14T20:58:26.179Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ea/20/783eccea7206ceeda42a09a4614e3da92889e4c54abe9dec2e5e53576e1a/pyobjc_framework_screentime-11.1-py2.py3-none-any.whl", hash = "sha256:50a4e4ab33d6643a52616e990aa1c697d5e3e8f9f9bdab8d631e6d42d8287b4f", size = 3949, upload-time = "2025-06-14T20:54:26.916Z" },
]

[[package]]
name = "pyobjc-framework-scriptingbridge"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/8e/c1/5b1dd01ff173df4c6676f97405113458918819cb2064c1735b61948e8800/pyobjc_framework_scriptingbridge-11.1.tar.gz", hash = "sha256:604445c759210a35d86d3e0dfcde0aac8e5e3e9d9e35759e0723952138843699", size = 23155, upload-time = "2025-06-14T20:58:26.812Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c1/64/31849063e3e81b4c312ce838dc98f0409c09eb33bc79dbb5261cb994a4c4/pyobjc_framework_scriptingbridge-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:226ba12d9cbd504411b702323b0507dd1690e81b4ce657c5f0d8b998c46cf374", size = 8323, upload-time = "2025-06-14T20:54:30.105Z" },
    { url = "https://files.pythonhosted.org/packages/d8/19/3003d4a137ce84fa8cb42a9c84f8c04e83c89749ab9cf93bc755016434b7/pyobjc_framework_scriptingbridge-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c2ba0ad3d3e4e3c6a43fe3e84ab02c5c4e74000bb6f130ae47bf82a3dcd4af98", size = 8337, upload-time = "2025-06-14T20:54:30.81Z" },
    { url = "https://files.pythonhosted.org/packages/e3/1c/0b90b4bcef7ea8fb80cb5f6fa0b73be075f2dffa2ba03580b37592dc8dad/pyobjc_framework_scriptingbridge-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:57f5401826e3a008d9cfb7c164187859cadc1b1f96194dc0a7c596f502548c26", size = 8485, upload-time = "2025-06-14T20:54:31.518Z" },
    { url = "https://files.pythonhosted.org/packages/bc/9d/22238e06780630ae3ec26d6af17df87d649fca0d9879caeaaf4f36b147c1/pyobjc_framework_scriptingbridge-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:a84d0a8ff4fa1f0016f5d797ad93e22e437212a2fc8e6417a3b8d68f89229680", size = 8346, upload-time = "2025-06-14T20:54:32.235Z" },
    { url = "https://files.pythonhosted.org/packages/07/e1/fc755423ffc3b28a4c2905c607e55cbed471edc025ec5c0849de4bea1230/pyobjc_framework_scriptingbridge-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:5381e9be1299e1134489e4d46662c649613214265b3b691264cfba0b083929f5", size = 8499, upload-time = "2025-06-14T20:54:32.918Z" },
]

[[package]]
name = "pyobjc-framework-searchkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-coreservices" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6e/20/61b73fddae0d1a94f5defb0cd4b4f391ec03bfcce7ebe830cb827d5e208a/pyobjc_framework_searchkit-11.1.tar.gz", hash = "sha256:13a194eefcf1359ce9972cd92f2aadddf103f3efb1b18fd578ba5367dff3c10c", size = 30918, upload-time = "2025-06-14T20:58:27.447Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2b/ed/a118d275a9132c8f5adcd353e4d9e844777068e33d51b195f46671161a7f/pyobjc_framework_searchkit-11.1-py2.py3-none-any.whl", hash = "sha256:9c9d6ca71cef637ccc3627225fb924a460b3d0618ed79bb0b3c12fcbe9270323", size = 3714, upload-time = "2025-06-14T20:54:34.329Z" },
]

[[package]]
name = "pyobjc-framework-security"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ee/6f/ba50ed2d9c1192c67590a7cfefa44fc5f85c776d1e25beb224dec32081f6/pyobjc_framework_security-11.1.tar.gz", hash = "sha256:dabcee6987c6bae575e2d1ef0fcbe437678c4f49f1c25a4b131a5e960f31a2da", size = 302291, upload-time = "2025-06-14T20:58:28.506Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/35/16/7fc52ab1364ada5885bf9b4c9ea9da3ad892b847c9b86aa59e086b16fc11/pyobjc_framework_security-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2eb4ba6d8b221b9ad5d010e026247e8aa26ee43dcaf327e848340ed227d22d7e", size = 41222, upload-time = "2025-06-14T20:54:37.032Z" },
    { url = "https://files.pythonhosted.org/packages/3f/d8/cb20b4c4d15b2bdc7e39481159e50a933ddb87e4702d35060c254b316055/pyobjc_framework_security-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:158da3b2474e2567fd269531c4ee9f35b8ba4f1eccbd1fb4a37c85a18bf1243c", size = 41221, upload-time = "2025-06-14T20:54:37.803Z" },
    { url = "https://files.pythonhosted.org/packages/cb/3c/d13d6870f5d66f5379565887b332f86f16d666dc50a1944d7e3a1462e76c/pyobjc_framework_security-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:141cc3ee08627ae0698264efc3dbbaf28d2255e0fe690e336eb8f0f387c4af01", size = 42099, upload-time = "2025-06-14T20:54:38.627Z" },
    { url = "https://files.pythonhosted.org/packages/f0/3d/2f61d4566e80f203d0e05ddd788037dc06a94d200edac25d2747fd79b5aa/pyobjc_framework_security-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:858a18303711eb69d18d1a64cf8bb2202f64a3bd1c82203c511990dbd8326514", size = 41288, upload-time = "2025-06-14T20:54:39.432Z" },
    { url = "https://files.pythonhosted.org/packages/15/44/99ef33a5319ed2cb6c0a51ed36214adf21ccb37cce970b1acc8bfe57ce23/pyobjc_framework_security-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4db1ebf6395cd370139cb35ff172505fc449c7fdf5d3a28f2ada8a30ef132cd0", size = 42849, upload-time = "2025-06-14T20:54:40.174Z" },
]

[[package]]
name = "pyobjc-framework-securityfoundation"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/5c/d4/19591dd0938a45b6d8711ef9ae5375b87c37a55b45d79c52d6f83a8d991f/pyobjc_framework_securityfoundation-11.1.tar.gz", hash = "sha256:b3c4cf70735a93e9df40f3a14478143959c415778f27be8c0dc9ae0c5b696b92", size = 13270, upload-time = "2025-06-14T20:58:29.304Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6c/ab/23db6b1c09810d6bcc4eab96e62487fb4284b57e447eabe6c001cb41e36d/pyobjc_framework_securityfoundation-11.1-py2.py3-none-any.whl", hash = "sha256:25f2cf10f80c122f462e9d4d43efe9fd697299c194e0c357e76650e234e6d286", size = 3772, upload-time = "2025-06-14T20:54:41.732Z" },
]

[[package]]
name = "pyobjc-framework-securityinterface"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a1/be/c846651c3e7f38a637c40ae1bcda9f14237c2395637c3a188df4f733c727/pyobjc_framework_securityinterface-11.1.tar.gz", hash = "sha256:e7aa6373e525f3ae05d71276e821a6348c53fec9f812b90eec1dbadfcb507bc9", size = 37648, upload-time = "2025-06-14T20:58:29.932Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6f/ab/48b8027a24f3f8924f5be5f97217961b4ed23e6be49b3bd94ee8a0d56a1e/pyobjc_framework_securityinterface-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:26056441b325029da06a7c7b8dd1a0c9a4ad7d980596c1b04d132a502b4cacc0", size = 10837, upload-time = "2025-06-14T20:54:44.052Z" },
    { url = "https://files.pythonhosted.org/packages/31/2e/de226a3caa47b4a800c8e6289b9fe30c71f10985dbc37379d5bd0781b470/pyobjc_framework_securityinterface-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:708dd1d65309f3d4043ecaf152591c240601a5d3da7ae7a500f511c54317537b", size = 10851, upload-time = "2025-06-14T20:54:45.254Z" },
    { url = "https://files.pythonhosted.org/packages/2a/9f/2d0c41ded78f9dc1e58d63b9d7ed55666b0d0d6ec78ce8938c7c4accdf59/pyobjc_framework_securityinterface-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e9ebfb32177eb06f5c894be97c6af3802f09b9890fce8e0956cc0e680af4eafd", size = 11183, upload-time = "2025-06-14T20:54:46.325Z" },
    { url = "https://files.pythonhosted.org/packages/f0/5d/2d45351564273c1bd24ffc691d0d932b0cdef5373cc0f0510239b93d5913/pyobjc_framework_securityinterface-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:0232f947b4f906097a5d758305097a8688835a52e0721b75ae3f1180eac30f50", size = 10885, upload-time = "2025-06-14T20:54:47.03Z" },
    { url = "https://files.pythonhosted.org/packages/ae/80/7b8dce55a83d1f6ed056f6dd5ec0a927ec0e4fbe60eba05ef1816cc0d959/pyobjc_framework_securityinterface-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:2c20bedead75de7bf1f2ceda562755f64c70ee86180ed45480dc9dbc55609a0b", size = 11225, upload-time = "2025-06-14T20:54:47.731Z" },
]

[[package]]
name = "pyobjc-framework-securityui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-security" },
]
sdist = { url = "https://files.pythonhosted.org/packages/07/5b/3b5585d56e0bcaba82e0661224bbc7aaf29fba6b10498971dbe08b2b490a/pyobjc_framework_securityui-11.1.tar.gz", hash = "sha256:e80c93e8a56bf89e4c0333047b9f8219752dd6de290681e9e2e2b2e26d69e92d", size = 12179, upload-time = "2025-06-14T20:58:30.928Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d0/a4/c9fcc42065b6aed73b14b9650c1dc0a4af26a30d418cbc1bab33621b461c/pyobjc_framework_securityui-11.1-py2.py3-none-any.whl", hash = "sha256:3cdb101b03459fcf8e4064b90021d06761003f669181e02f43ff585e6ba2403d", size = 3581, upload-time = "2025-06-14T20:54:49.474Z" },
]

[[package]]
name = "pyobjc-framework-sensitivecontentanalysis"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/56/7b/e28f6b30d99e9d464427a07ada82b33cd3292f310bf478a1824051d066b9/pyobjc_framework_sensitivecontentanalysis-11.1.tar.gz", hash = "sha256:5b310515c7386f7afaf13e4632d7d9590688182bb7b563f8026c304bdf317308", size = 12796, upload-time = "2025-06-14T20:58:31.488Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3c/63/76a939ecac74ca079702165330c692ad2c05ff9b2b446a72ddc8cdc63bb9/pyobjc_framework_sensitivecontentanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:dbb78f5917f986a63878bb91263bceba28bd86fc381bad9461cf391646db369f", size = 3852, upload-time = "2025-06-14T20:54:50.75Z" },
]

[[package]]
name = "pyobjc-framework-servicemanagement"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/c6/32e11599d9d232311607b79eb2d1d21c52eaaf001599ea85f8771a933fa2/pyobjc_framework_servicemanagement-11.1.tar.gz", hash = "sha256:90a07164da49338480e0e135b445acc6ae7c08549a2037d1e512d2605fedd80a", size = 16645, upload-time = "2025-06-14T20:58:32.062Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/f1/222462f5afcb6cb3c1fc9e6092dfcffcc7eb9db8bd2cef8c1743a22fbe95/pyobjc_framework_servicemanagement-11.1-py2.py3-none-any.whl", hash = "sha256:104f56557342a05ad68cd0c9daf63b7f4678957fe1f919f03a872f1607a50710", size = 5338, upload-time = "2025-06-14T20:54:51.614Z" },
]

[[package]]
name = "pyobjc-framework-sharedwithyou"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-sharedwithyoucore" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fe/a5/e299fbd0c13d4fac9356459f21372f6eef4279d0fbc99ba316d88dfbbfb4/pyobjc_framework_sharedwithyou-11.1.tar.gz", hash = "sha256:ece3a28a3083d0bcad0ac95b01f0eb699b9d2d0c02c61305bfd402678753ff6e", size = 34216, upload-time = "2025-06-14T20:58:32.75Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/57/44/211e1f18676e85d3656671fc0c954ced2cd007e55f1b0b6b2e4d0a0852eb/pyobjc_framework_sharedwithyou-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:99e1749187ae370be7b9c55dd076d1b8143f0d8db3e83f52540586f32e7abb33", size = 8740, upload-time = "2025-06-14T20:54:53.879Z" },
    { url = "https://files.pythonhosted.org/packages/6f/da/1a2f2ae024e0206e1bcaba27aac2ebadf8bceb0ee05d03be2250e8c3d1a3/pyobjc_framework_sharedwithyou-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:c1a1770aa2c417f17010623414fb12943570baa726d8780dd7446ba5bcee8c3d", size = 8759, upload-time = "2025-06-14T20:54:54.631Z" },
    { url = "https://files.pythonhosted.org/packages/48/85/d54efa902f5dd18a99478eb4fd0befda07dcd2672b1c3ed00ec88280fed0/pyobjc_framework_sharedwithyou-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:63b1cb673b844ebfeddc032d0539f913bbd6b67ab2a310a1fcff7842dba9c714", size = 8909, upload-time = "2025-06-14T20:54:55.359Z" },
    { url = "https://files.pythonhosted.org/packages/df/a0/03d0277bae4b49f9ec6dd078c7b66ffbeca71ffe47c206222697a7a563e2/pyobjc_framework_sharedwithyou-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:481362f0bde6def86634fc687abe6f4dee650c09c22b48bfe5af5322f9947cef", size = 8807, upload-time = "2025-06-14T20:54:56.041Z" },
    { url = "https://files.pythonhosted.org/packages/f0/66/0873bad696dfa6f8b597c9de5b0a1e1529f4ed21bf54c8389ec43499298d/pyobjc_framework_sharedwithyou-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:70421a8fd326afd99eeae273b693a7b4d2d200c38e883d8219a84123a4ba0861", size = 8955, upload-time = "2025-06-14T20:54:57.351Z" },
]

[[package]]
name = "pyobjc-framework-sharedwithyoucore"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/79/a3/1ca6ff1b785772c7c5a38a7c017c6f971b1eda638d6a0aab3bbde18ac086/pyobjc_framework_sharedwithyoucore-11.1.tar.gz", hash = "sha256:790050d25f47bda662a9f008b17ca640ac2460f2559a56b17995e53f2f44ed73", size = 29459, upload-time = "2025-06-14T20:58:33.422Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/70/3b2e13fcf393aa434b1cf5c29c6aaf65ee5b8361254df3a920ed436bb5e4/pyobjc_framework_sharedwithyoucore-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:dd18c588b29de322c25821934d6aa6d2bbbdbb89b6a4efacdb248b4115fc488d", size = 8512, upload-time = "2025-06-14T20:55:00.411Z" },
    { url = "https://files.pythonhosted.org/packages/b7/fc/feb2912fb9c7bbeb2099d2cb42ad28055c6e29504fcb92bd8a011fcba66a/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:a3fb0e745fd022fed48cc9a5e0dcbf8d1abcb5bfc192150e3a2584f4351791fc", size = 8527, upload-time = "2025-06-14T20:55:01.112Z" },
    { url = "https://files.pythonhosted.org/packages/f1/3f/0a8aa5d1b0eb07508c42e900d82a89e096b79fcafcd55e966d4d45476ae5/pyobjc_framework_sharedwithyoucore-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:6aee3df8bed97a74e1f79609f9884edcaab2d305db20bdcae39e47b3e513c559", size = 8672, upload-time = "2025-06-14T20:55:01.801Z" },
    { url = "https://files.pythonhosted.org/packages/64/f4/582ca62f3b154a5a0c46854c329aae07dddeadbced077394211644d4862b/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5a45c562c99017f8e057d4080012b63a9bb660c696334707c54d7b4018ca1017", size = 8569, upload-time = "2025-06-14T20:55:02.52Z" },
    { url = "https://files.pythonhosted.org/packages/98/3a/b64eccedc362d0427cd67dfa4531b3eb935a2c31419f3f5803f40dcb0803/pyobjc_framework_sharedwithyoucore-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e19bfc74f392546ca4b7ea5271d4802617445ad493428370eafd3cddd4d977e", size = 8719, upload-time = "2025-06-14T20:55:03.624Z" },
]

[[package]]
name = "pyobjc-framework-shazamkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/de/08/ba739b97f1e441653bae8da5dd1e441bbbfa43940018d21edb60da7dd163/pyobjc_framework_shazamkit-11.1.tar.gz", hash = "sha256:c6e3c9ab8744d9319a89b78ae6f185bb5704efb68509e66d77bcd1f84a9446d6", size = 25797, upload-time = "2025-06-14T20:58:34.086Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/89/b7/594b8bdc406603a7a07cdb33f2be483fed16aebc35aeb087385fc9eca844/pyobjc_framework_shazamkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:b323f5409b01711aa2b6e2113306084fab2cc83fa57a0c3d55bd5876358b68d8", size = 8560, upload-time = "2025-06-14T20:55:07.564Z" },
    { url = "https://files.pythonhosted.org/packages/8c/fa/49ba8d1f9e257a12267773d6682e170fba441c7ea72d6fe58da9f4bf6f10/pyobjc_framework_shazamkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8bac17f285742e0f13a54c7085ef3035d8034ffc43d18d3d68fb41283c5064ff", size = 8573, upload-time = "2025-06-14T20:55:08.42Z" },
    { url = "https://files.pythonhosted.org/packages/22/47/eeae6a31a41cbaf29081145b8f54ddebf68a5eba19626dd9ba2c00fdc92b/pyobjc_framework_shazamkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b3304c3a67e3722b895d874f215dd4277b49cedddb72fa780a791ef79e5c3d45", size = 8726, upload-time = "2025-06-14T20:55:09.447Z" },
    { url = "https://files.pythonhosted.org/packages/b9/72/e4e4bca07808f0a930955ddfdd10cf6322096fced76bf06b52d379df850c/pyobjc_framework_shazamkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef51f461672234076b3791ad4be05adad20a2e24b9d7d93acd7bf18d7f9b1714", size = 8610, upload-time = "2025-06-14T20:55:10.14Z" },
    { url = "https://files.pythonhosted.org/packages/c4/f2/31e186b99ccf22cbceddea58edfdcbef6a336c12326e198e7c6fd18b5938/pyobjc_framework_shazamkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:f7d191fb187dbb05e3f88f546d5207618d65e270d7a4316b51b1171cc491e268", size = 8766, upload-time = "2025-06-14T20:55:10.833Z" },
]

[[package]]
name = "pyobjc-framework-social"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/07/2e/cc7707b7a40df392c579087947049f3e1f0e00597e7151ec411f654d8bef/pyobjc_framework_social-11.1.tar.gz", hash = "sha256:fbc09d7b00dad45b547f9b2329f4dcee3f5a50e2348de1870de0bd7be853a5b7", size = 14540, upload-time = "2025-06-14T20:58:35.116Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/86/1d/e1026c082a66075dbb7e57983c0aaaed3ee09f06c346743e8af24d1dc21a/pyobjc_framework_social-11.1-py2.py3-none-any.whl", hash = "sha256:ab5878c47d7a0639704c191cee43eeb259e09688808f0905c42551b9f79e1d57", size = 4444, upload-time = "2025-06-14T20:55:12.536Z" },
]

[[package]]
name = "pyobjc-framework-soundanalysis"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e0/d4/b9497dbb57afdf0d22f61bb6e776a6f46cf9294c890448acde5b46dd61f3/pyobjc_framework_soundanalysis-11.1.tar.gz", hash = "sha256:42cd25b7e0f343d8b59367f72b5dae96cf65696bdb8eeead8d7424ed37aa1434", size = 16539, upload-time = "2025-06-14T20:58:35.813Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/b4/7e8cf3a02e615239568fdf12497233bbd5b58082615cd28a0c7cd4636309/pyobjc_framework_soundanalysis-11.1-py2.py3-none-any.whl", hash = "sha256:6cf983c24fb2ad2aa5e7499ab2d30ff134d887fe91fd2641acf7472e546ab4e5", size = 4161, upload-time = "2025-06-14T20:55:13.342Z" },
]

[[package]]
name = "pyobjc-framework-speech"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/67/76/2a1fd7637b2c662349ede09806e159306afeebfba18fb062ad053b41d811/pyobjc_framework_speech-11.1.tar.gz", hash = "sha256:d382977208c3710eacea89e05eae4578f1638bb5a7b667c06971e3d34e96845c", size = 41179, upload-time = "2025-06-14T20:58:36.43Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/78/59/267f4699055beb39723ccbff70909ec3851e4adf17386f6ad85e5d983780/pyobjc_framework_speech-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:7726eff52cfa9cc7178ddcd1285cbc23b5f89ee55b4b850b0d2e90bb4f8e044b", size = 9180, upload-time = "2025-06-14T20:55:16.556Z" },
    { url = "https://files.pythonhosted.org/packages/ea/a6/c394c3973c42d86c7b0c5c673c5ce65d10671e59e174f1ba4e7ab61ae5df/pyobjc_framework_speech-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:3c80670dbad921bf1d4954a9de29525acb53ee84e064a95fbbdfddff1db2f14f", size = 9198, upload-time = "2025-06-14T20:55:17.581Z" },
    { url = "https://files.pythonhosted.org/packages/95/e9/3e47e2e3337080e45dd9153c7f465d16c40ce74b11ac53c4663554dab0bd/pyobjc_framework_speech-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f19778a4ace37c538a34a10ac1f595c80b83489210e6fa60c703399aee264c7e", size = 9355, upload-time = "2025-06-14T20:55:18.27Z" },
    { url = "https://files.pythonhosted.org/packages/b1/81/dfc795916cfb5d9eb98809e93b380948422d3901ce60ec168681530b6fd5/pyobjc_framework_speech-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:f36ca8a3cfc12b7a5cdf00712eec3ad0fac34e3da36b5737c5302e224525aa70", size = 9249, upload-time = "2025-06-14T20:55:18.961Z" },
    { url = "https://files.pythonhosted.org/packages/e0/cd/29d5a50d9c596eef5d9b9c1442169908e99bc79edc58b573e393829b1f6b/pyobjc_framework_speech-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:80e577e3dfc1c10a1280deae172cdb64e105f99f47343099e3968b720a3f68da", size = 9401, upload-time = "2025-06-14T20:55:20.242Z" },
]

[[package]]
name = "pyobjc-framework-spritekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/16/02/2e253ba4f7fad6efe05fd5fcf44aede093f6c438d608d67c6c6623a1846d/pyobjc_framework_spritekit-11.1.tar.gz", hash = "sha256:914da6e846573cac8db5e403dec9a3e6f6edf5211f9b7e429734924d00f65108", size = 130297, upload-time = "2025-06-14T20:58:37.113Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f1/fe/39d92bf40ec7a6116f89fd95053321f7c00c50c10d82b9adfa0f9ebdb10c/pyobjc_framework_spritekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:8b470a890db69e70ef428dfff88da499500fca9b2d44da7120dc588d13a2dbdb", size = 17776, upload-time = "2025-06-14T20:55:23.639Z" },
    { url = "https://files.pythonhosted.org/packages/3f/c1/56490cce24e34e8c4c8c6a0f4746cd3a8bb5c2403e243c99f4dfa0cd147f/pyobjc_framework_spritekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2277e74d7be426181ae5ca7dd9d6c776426e8e825ad83b6046a7cb999015f27d", size = 17798, upload-time = "2025-06-14T20:55:24.407Z" },
    { url = "https://files.pythonhosted.org/packages/75/dc/2ddd3aec417ebb92fd37f687c3e41e051d5e8b761bf2af63b1eb21e20cf4/pyobjc_framework_spritekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d6ea27fc202b40945729db50fdc6f75a0a11a07149febf4b99e14caf96ef33b0", size = 18068, upload-time = "2025-06-14T20:55:25.541Z" },
    { url = "https://files.pythonhosted.org/packages/f1/db/f26835b6c4e169bb451878973e109deb5c8e14c41042d97795200f4d3bbb/pyobjc_framework_spritekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:e04d0825109a0158e551e9e2a61c56e83eadfdc5a44a47b64cb410b0498d33be", size = 17835, upload-time = "2025-06-14T20:55:26.295Z" },
    { url = "https://files.pythonhosted.org/packages/4c/c3/e920aacda0bf97b37396eafb93676f359a8407a8e04fae6f9c80c25ba922/pyobjc_framework_spritekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:4e3673196b7cbc007e4aa7f14d711f3cda00e32e120bc4f6e896d54edd517c61", size = 18092, upload-time = "2025-06-14T20:55:27.04Z" },
]

[[package]]
name = "pyobjc-framework-storekit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/44/a0/58cab9ebc9ac9282e1d4734b1987d1c3cd652b415ec3e678fcc5e735d279/pyobjc_framework_storekit-11.1.tar.gz", hash = "sha256:85acc30c0bfa120b37c3c5ac693fe9ad2c2e351ee7a1f9ea6f976b0c311ff164", size = 76421, upload-time = "2025-06-14T20:58:37.86Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ac/61/6404aac6857ea43798882333bcc26bfd3c9c3a1efc7a575cbf3e53538e2a/pyobjc_framework_storekit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:5ca3373272b6989917c88571ca170ce6d771180fe1a2b44c7643fe084569b93e", size = 11868, upload-time = "2025-06-14T20:55:30.454Z" },
    { url = "https://files.pythonhosted.org/packages/6b/52/23acdf128a5b04059b2a3b38928afbff0afb50da439b597e25cdff1e9148/pyobjc_framework_storekit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2e2607116b0d53d7fda2fc48e37b1deb1d26a60e7b723a6b7c391a3f48b2ac3b", size = 11882, upload-time = "2025-06-14T20:55:31.523Z" },
    { url = "https://files.pythonhosted.org/packages/48/04/e7407f5c11a56c9a3a6b4328ec95dbf01ea6f88ac0ff5dc5089e9c8d0a61/pyobjc_framework_storekit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4944bd1fd01f486623453b68accf4445d3c5686714820c8329a0c4e4672d6fff", size = 12129, upload-time = "2025-06-14T20:55:32.213Z" },
    { url = "https://files.pythonhosted.org/packages/7a/de/8910a6f54647c0adc2aeb6846afc94a99d17470dd3d905e8b1caeccfcd98/pyobjc_framework_storekit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d312c392962e15fc842d11b0f7d937e3bd9f3ed3a80f7a6be77518475564f04d", size = 11939, upload-time = "2025-06-14T20:55:33.075Z" },
    { url = "https://files.pythonhosted.org/packages/b4/12/c04fa481f7ec80beaff532734dde19303133547ae16414934d05d0df046f/pyobjc_framework_storekit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:be6c894a9f9c2b40e300005c3a3cf46f352e1711f65c0b7a8dd5035d1f6333aa", size = 12121, upload-time = "2025-06-14T20:55:34.087Z" },
]

[[package]]
name = "pyobjc-framework-symbols"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/cd/af/7191276204bd3e7db1d0a3e490a869956606f77f7a303a04d92a5d0c3f7b/pyobjc_framework_symbols-11.1.tar.gz", hash = "sha256:0e09b7813ef2ebdca7567d3179807444dd60f3f393202b35b755d4e1baf99982", size = 13377, upload-time = "2025-06-14T20:58:38.542Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9a/6a/c91f64ef9b8cd20245b88e392c66cb2279c511724f4ea2983d92584d6f3e/pyobjc_framework_symbols-11.1-py2.py3-none-any.whl", hash = "sha256:1de6fc3af15fc8d5fd4869663a3250311844ec33e99ec8a1991a352ab61d641d", size = 3312, upload-time = "2025-06-14T20:55:35.456Z" },
]

[[package]]
name = "pyobjc-framework-syncservices"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coredata" },
]
sdist = { url = "https://files.pythonhosted.org/packages/69/45/cd9fa83ed1d75be7130fb8e41c375f05b5d6621737ec37e9d8da78676613/pyobjc_framework_syncservices-11.1.tar.gz", hash = "sha256:0f141d717256b98c17ec2eddbc983c4bd39dfa00dc0c31b4174742e73a8447fe", size = 57996, upload-time = "2025-06-14T20:58:39.146Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/01/2b/6d7d65c08a9c51eed12eb7f83eaa48deaed621036f77221b3b0346c3f6c2/pyobjc_framework_syncservices-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:03124c8c7c7ce837f51e1c9bdcf84c6f1d5201f92c8a1c172ec34908d5e57415", size = 13496, upload-time = "2025-06-14T20:55:37.83Z" },
    { url = "https://files.pythonhosted.org/packages/99/7b/88e89b81b5a6ee7da3b452c1619ec22936a8dd4384afd67f6019472655b8/pyobjc_framework_syncservices-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:711d493c7967682bee605c5909a49d268d9b3dd3cb7a71d8ab5dbe01a069eb44", size = 13511, upload-time = "2025-06-14T20:55:38.55Z" },
    { url = "https://files.pythonhosted.org/packages/bf/3c/6056913cea9fce52f77649b81c54c6282f2eb1b26e7ca17c5c1015123375/pyobjc_framework_syncservices-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a0ff222472b2cb5c345c92ae4bde245f4181843379f4fd9462cd5c096ed7b2f1", size = 13681, upload-time = "2025-06-14T20:55:39.279Z" },
    { url = "https://files.pythonhosted.org/packages/63/b1/c9f74441515efd2b05b797df09fff37b61aa583dac6462152063ab47b80d/pyobjc_framework_syncservices-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:24c2b62e94d9e0e5e64abbf6d1f9994212b2a5cb8cad5a8d0394d694b20731b5", size = 13576, upload-time = "2025-06-14T20:55:39.994Z" },
    { url = "https://files.pythonhosted.org/packages/36/0f/812a2151539aa46363fe4abaad99344380a5c2287840c98a5a021bf3ed0f/pyobjc_framework_syncservices-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:e5b29d6e8fe5b0015dcac5485e4fe6ede35bae7beeb647fb81d86120365029ea", size = 13754, upload-time = "2025-06-14T20:55:41.223Z" },
]

[[package]]
name = "pyobjc-framework-systemconfiguration"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e2/3d/41590c0afc72e93d911348fbde0c9c1071ff53c6f86df42df64b21174bb9/pyobjc_framework_systemconfiguration-11.1.tar.gz", hash = "sha256:f30ed0e9a8233fecb06522e67795918ab230ddcc4a18e15494eff7532f4c3ae1", size = 143410, upload-time = "2025-06-14T20:58:39.917Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b9/61/0e9841bf1c7597f380a6dcefcc9335b6a909f20d9bdf07910cddc8552b42/pyobjc_framework_systemconfiguration-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:6881929b828a566bf1349f09db4943e96a2b33f42556e1f7f6f28b192420f6fc", size = 21639, upload-time = "2025-06-14T20:55:44.678Z" },
    { url = "https://files.pythonhosted.org/packages/1c/eb/4480a1ab5baba4b9e75bb7f4f667073db5702cf521ddc99941575167585d/pyobjc_framework_systemconfiguration-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ab2ff52e4228f42182b7ef398d0da504f9f8f4a889963422af9aa1f495668db2", size = 21646, upload-time = "2025-06-14T20:55:45.426Z" },
    { url = "https://files.pythonhosted.org/packages/b7/00/40d433a160c4d3c156008d375aa0279f46343c69cecb464e59ab1a0b3063/pyobjc_framework_systemconfiguration-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:c236f19cadc9fff56c0afb3e4ad6f8c8e11c5679e31ed413fe6876bf2ea73353", size = 22059, upload-time = "2025-06-14T20:55:46.203Z" },
    { url = "https://files.pythonhosted.org/packages/60/d0/18ad65359d0fd71c67f14b02bf03efdd6e472185204c82f5885343798d52/pyobjc_framework_systemconfiguration-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:ef266e9f83c2fc9a999709626138b427ff052a0acf4851d797c3a7654878c046", size = 21667, upload-time = "2025-06-14T20:55:47.303Z" },
    { url = "https://files.pythonhosted.org/packages/e6/cf/4dcf61dd20bfa8d95e4328f431b59119bc2118da9dc570738428ec556b80/pyobjc_framework_systemconfiguration-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b994c613b5bea9f1c9a64f57f373563c7f424ffae5e4cb20e76c8448a35543f7", size = 22056, upload-time = "2025-06-14T20:55:48.055Z" },
]

[[package]]
name = "pyobjc-framework-systemextensions"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/57/4609fd9183383616b1e643c2489ad774335f679523a974b9ce346a6d4d5b/pyobjc_framework_systemextensions-11.1.tar.gz", hash = "sha256:8ff9f0aad14dcdd07dd47545c1dd20df7a286306967b0a0232c81fcc382babe6", size = 23062, upload-time = "2025-06-14T20:58:40.686Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/40/d9be444b39ec12d68b5e4f712b71d6c00d654936ff5744ea380c1bfabf06/pyobjc_framework_systemextensions-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3a2b1e84e4a118bfe13efb9f2888b065dc937e2a7e60afd4d0a82b51b8301a10", size = 9130, upload-time = "2025-06-14T20:55:51.127Z" },
    { url = "https://files.pythonhosted.org/packages/7d/23/f615d69b3a86e75af234149fc12c8dfde8f346148e4eb185696a9c87e824/pyobjc_framework_systemextensions-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:2ed65857244f18b88107e5d3ea8ea21c9da662490895b430e376423ee7c0b963", size = 9154, upload-time = "2025-06-14T20:55:51.798Z" },
    { url = "https://files.pythonhosted.org/packages/3c/08/2719c95d57f404d880c80da4250ff122ff318307e7a9b8ceef54d56fdb7f/pyobjc_framework_systemextensions-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:9aa7595de4f8f6a252c50419c0343f7326c6a4de47da5b933a17880d1cadfa36", size = 9315, upload-time = "2025-06-14T20:55:52.494Z" },
    { url = "https://files.pythonhosted.org/packages/88/ff/a984a96f49b27d9c79ab97aa484bac27d3b4f1de14b9a1080de3622e63f1/pyobjc_framework_systemextensions-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:97c1b5f415f3981d0426516e014e94392f054f3898252bf6c88c3f50700c1d70", size = 9204, upload-time = "2025-06-14T20:55:53.173Z" },
    { url = "https://files.pythonhosted.org/packages/d9/57/574b1c59afac30e605c476c5911a69e70d338adf5ff810042f5d55e77871/pyobjc_framework_systemextensions-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:1801413066d1cbf2a0319e228060820c51ea0fb27aec339716d8c82f2e1b3125", size = 9366, upload-time = "2025-06-14T20:55:54.251Z" },
]

[[package]]
name = "pyobjc-framework-threadnetwork"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e7/a4/5400a222ced0e4f077a8f4dd0188e08e2af4762e72ed0ed39f9d27feefc9/pyobjc_framework_threadnetwork-11.1.tar.gz", hash = "sha256:73a32782f44b61ca0f8a4a9811c36b1ca1cdcf96c8a3ba4de35d8e8e58a86ad5", size = 13572, upload-time = "2025-06-14T20:58:41.311Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b0/f0/b7a577d00bdb561efef82b046a75f627a60de53566ab2d9e9ddd5bd11b66/pyobjc_framework_threadnetwork-11.1-py2.py3-none-any.whl", hash = "sha256:55021455215a0d3ad4e40152f94154e29062e73655558c5f6e71ab097d90083e", size = 3751, upload-time = "2025-06-14T20:55:55.643Z" },
]

[[package]]
name = "pyobjc-framework-uniformtypeidentifiers"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c5/4f/066ed1c69352ccc29165f45afb302f8c9c2b5c6f33ee3abfa41b873c07e5/pyobjc_framework_uniformtypeidentifiers-11.1.tar.gz", hash = "sha256:86c499bec8953aeb0c95af39b63f2592832384f09f12523405650b5d5f1ed5e9", size = 20599, upload-time = "2025-06-14T20:58:41.945Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/de/3b/b63b8137dd9f455d5abece6702c06c6b613fac6fda1319aaa2f79d00c380/pyobjc_framework_uniformtypeidentifiers-11.1-py2.py3-none-any.whl", hash = "sha256:6e2e8ea89eb8ca03bc2bc8e506fff901e71d916276475c8d81fbf0280059cb4c", size = 4891, upload-time = "2025-06-14T20:55:56.432Z" },
]

[[package]]
name = "pyobjc-framework-usernotifications"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b4/4c/e7e180fcd06c246c37f218bcb01c40ea0213fde5ace3c09d359e60dcaafd/pyobjc_framework_usernotifications-11.1.tar.gz", hash = "sha256:38fc763afa7854b41ddfca8803f679a7305d278af8a7ad02044adc1265699996", size = 55428, upload-time = "2025-06-14T20:58:42.572Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/03/af/a54e343a7226dc65a65f7a561c060f8c96cb9f92f41ce2242d20d82ae594/pyobjc_framework_usernotifications-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:ce6006989fd4a59ec355f6797ccdc9946014ea5241ff7875854799934dbba901", size = 9606, upload-time = "2025-06-14T20:55:59.088Z" },
    { url = "https://files.pythonhosted.org/packages/d1/fb/ae1ea7f7c511714c1502fa9c4856c6b3dfe110ff7cc094070fec5ad496b8/pyobjc_framework_usernotifications-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9efa3004059a8fe3f3c52f638f0401dbcdbc7b2f539587c8868da2486a64d674", size = 9628, upload-time = "2025-06-14T20:55:59.807Z" },
    { url = "https://files.pythonhosted.org/packages/e5/46/4934930848d74aeea32435378154501fcb3dbd77f759c4aa09b99e094310/pyobjc_framework_usernotifications-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62a4bd242b761a6f00a4374a369391346d225d68be07691e042ec7db452084c8", size = 9793, upload-time = "2025-06-14T20:56:00.496Z" },
    { url = "https://files.pythonhosted.org/packages/f2/f7/fadd62a479322bc8bf20684c6a87a1eb40b28c03899a8cc3d5b6fe781d93/pyobjc_framework_usernotifications-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:dcdcb657d2fa47108e4ef93ec3320025576857e8f69a15f082f5eda930b35e86", size = 9666, upload-time = "2025-06-14T20:56:01.176Z" },
    { url = "https://files.pythonhosted.org/packages/72/c3/406d196d094cf8c30bbc815a8ca8ef57bfa21c2494f93ff1125f78f8a922/pyobjc_framework_usernotifications-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:bad5e650c014757159523466e5b2c127e066045e2a5579a5cac9aeca46bda017", size = 9852, upload-time = "2025-06-14T20:56:01.871Z" },
]

[[package]]
name = "pyobjc-framework-usernotificationsui"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-usernotifications" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d2/c4/03d97bd3adcee9b857533cb42967df0d019f6a034adcdbcfca2569d415b2/pyobjc_framework_usernotificationsui-11.1.tar.gz", hash = "sha256:18e0182bddd10381884530d6a28634ebb3280912592f8f2ad5bac2a9308c6a65", size = 14123, upload-time = "2025-06-14T20:58:43.267Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9d/2c/0bb489b5ac4daf83b113018701ce30a0cb4bf47c615c92c5844a16e0a012/pyobjc_framework_usernotificationsui-11.1-py2.py3-none-any.whl", hash = "sha256:b84d73d90ab319acf8fad5c59b7a5e2b6023fbb2efd68c58b532e3b3b52f647a", size = 3914, upload-time = "2025-06-14T20:56:03.978Z" },
]

[[package]]
name = "pyobjc-framework-videosubscriberaccount"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/aa/00/cd9d93d06204bbb7fe68fb97022b0dd4ecdf8af3adb6d70a41e22c860d55/pyobjc_framework_videosubscriberaccount-11.1.tar.gz", hash = "sha256:2dd78586260fcee51044e129197e8bf2e157176e02babeec2f873afa4235d8c6", size = 28856, upload-time = "2025-06-14T20:58:43.903Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4b/dc/b409dee6dd58a5db2e9a681bde8894c9715468689f18e040f7d252794c3d/pyobjc_framework_videosubscriberaccount-11.1-py2.py3-none-any.whl", hash = "sha256:d5a95ae9f2a6f0180a5bbb10e76c064f0fd327aae00a2fe90aa7b65ed4dad7ef", size = 4695, upload-time = "2025-06-14T20:56:06.027Z" },
]

[[package]]
name = "pyobjc-framework-videotoolbox"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coremedia" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e5/e3/df9096f54ae1f27cab8f922ee70cbda5d80f8c1d12734c38580829858133/pyobjc_framework_videotoolbox-11.1.tar.gz", hash = "sha256:a27985656e1b639cdb102fcc727ebc39f71bb1a44cdb751c8c80cc9fe938f3a9", size = 88551, upload-time = "2025-06-14T20:58:44.566Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1f/cf/569babadbf1f9598f62c400ee02da19d4ab5f36276978c81080999399df9/pyobjc_framework_videotoolbox-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:c55285c3c78183fd2a092d582e30b562777a82985cccca9e7e99a0aff2601591", size = 17432, upload-time = "2025-06-14T20:56:08.457Z" },
    { url = "https://files.pythonhosted.org/packages/b1/32/1a3d1a448d3cbcaf5c2a4ceaaad32817df21739099e187bbe6e3fd03d6fd/pyobjc_framework_videotoolbox-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:65a96385e80cb9ad3eab7d1f3156452ff805a925c9ca287ff1491a97cca191ba", size = 17450, upload-time = "2025-06-14T20:56:09.239Z" },
    { url = "https://files.pythonhosted.org/packages/64/d9/530b561bea7b8690ca976570466e42fa226fc60fe3fef3d14beaf719dc99/pyobjc_framework_videotoolbox-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e282cb07f6a51647ac19a3b5d31e26f1619285bac24171e403921d671e4756d9", size = 17668, upload-time = "2025-06-14T20:56:09.98Z" },
    { url = "https://files.pythonhosted.org/packages/21/de/478ead66538d665860bfc8fdb7c66a93bc07a9b32bd4150ee181bd16a66b/pyobjc_framework_videotoolbox-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:31acfb12cea4f0624ecb92e74404f15e2755fbf0a3f4133dc93add44cf4a6a9f", size = 17452, upload-time = "2025-06-14T20:56:10.738Z" },
    { url = "https://files.pythonhosted.org/packages/6d/32/bd465a698e680f95df87b3948dc4ced5f95dc813a88987355ffee5e1638c/pyobjc_framework_videotoolbox-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:0e54bd6cfcbdda4add24e8e873baab11dfb436633100cc6664f3c068e615a6ff", size = 17645, upload-time = "2025-06-14T20:56:11.507Z" },
]

[[package]]
name = "pyobjc-framework-virtualization"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f1/ff/57214e8f42755eeaad516a7e673dae4341b8742005d368ecc22c7a790b0b/pyobjc_framework_virtualization-11.1.tar.gz", hash = "sha256:4221ee5eb669e43a2ff46e04178bec149af2d65205deb5d4db5fa62ea060e022", size = 78633, upload-time = "2025-06-14T20:58:45.358Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c8/4f/fe1930f4ce2c7d2f4c34bb53adf43f412bc91364e8e4cb450a7c8a6b8b59/pyobjc_framework_virtualization-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:59df6702b3e63200752be7d9c0dc590cb4c3b699c886f9a8634dd224c74b3c3c", size = 13084, upload-time = "2025-06-14T20:56:14.617Z" },
    { url = "https://files.pythonhosted.org/packages/4f/33/6d9f4177983d8894d217b212c25cbb91004cb1103c865961f03360aff68b/pyobjc_framework_virtualization-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:12a5ef32d2b7a56b675ea34fcb68bb9dddb7cf2c0a5ac5131f35551767bdacf1", size = 13093, upload-time = "2025-06-14T20:56:15.322Z" },
    { url = "https://files.pythonhosted.org/packages/78/af/b9e1b6fa9afb4a6557e3bc1e7e8409108ecf416db5a8a9c6ef4d25dd16af/pyobjc_framework_virtualization-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:790bd2e42e8c5890319f8c576d5e171f87f95655e6fc55cf19a5f85f9e23558a", size = 13284, upload-time = "2025-06-14T20:56:16.052Z" },
    { url = "https://files.pythonhosted.org/packages/19/d7/9cadb62789974cb7ff65435e4b000d34cf9ec43e46ec2eb73de1620ab6a0/pyobjc_framework_virtualization-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:5f35d823003a613bde27c2c699a8a7de45dc2bdd2e1121e0c4a337b877dfc64e", size = 13111, upload-time = "2025-06-14T20:56:17.128Z" },
    { url = "https://files.pythonhosted.org/packages/2c/ee/39e84b673a33a10f518ecf5f7398a6a6864d2f23c79996c36809677678a1/pyobjc_framework_virtualization-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:b2e7ab5204fe80249dd8d031b761cf9c0106d0d5e61d88930e0f334f5060d820", size = 13299, upload-time = "2025-06-14T20:56:17.849Z" },
]

[[package]]
name = "pyobjc-framework-vision"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
    { name = "pyobjc-framework-coreml" },
    { name = "pyobjc-framework-quartz" },
]
sdist = { url = "https://files.pythonhosted.org/packages/40/a8/7128da4d0a0103cabe58910a7233e2f98d18c590b1d36d4b3efaaedba6b9/pyobjc_framework_vision-11.1.tar.gz", hash = "sha256:26590512ee7758da3056499062a344b8a351b178be66d4b719327884dde4216b", size = 133721, upload-time = "2025-06-14T20:58:46.095Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a2/b5/54c0227a695557ea3065bc035b20a5c256f6f3b861e095eee1ec4b4d8cee/pyobjc_framework_vision-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:df076c3e3e672887182953efc934c1f9683304737e792ec09a29bfee90d2e26a", size = 16829, upload-time = "2025-06-14T20:56:21.355Z" },
    { url = "https://files.pythonhosted.org/packages/20/cf/58ace43525ab073b39df9a740e855ebe83ed78f041d619644af3c60d9013/pyobjc_framework_vision-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1e5617e37dd2a7cff5e69e9aab039ea74b39ccdc528f6c828f2b60c1254e61e5", size = 16852, upload-time = "2025-06-14T20:56:22.081Z" },
    { url = "https://files.pythonhosted.org/packages/99/c3/4aeaac1d53766125870aadbe3a4a02d4bca373b18753d32281f77e095976/pyobjc_framework_vision-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:dfd148a6df30ac70a9c41dd90a6c8f8c7f339bd9ca6829629a902f272e02b6b4", size = 16993, upload-time = "2025-06-14T20:56:22.818Z" },
    { url = "https://files.pythonhosted.org/packages/75/29/bd70761b455067f1f0cb90a7c1983152b0e42b1f05ff91aa42c994a3f97d/pyobjc_framework_vision-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:d1f8fdccc6135fdbfd66d8f21240d6c84465cb8e116a8e5b43601aed020051e5", size = 16847, upload-time = "2025-06-14T20:56:23.572Z" },
    { url = "https://files.pythonhosted.org/packages/23/e1/72d2410377497b04ecd9718d8784a9d31bce36bbce0cb77c4e4fbcce7070/pyobjc_framework_vision-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:d00830c71a30fc893b3c5ee65119c7e5e5a95a16af53b8e56a0e58cff57e3b56", size = 16995, upload-time = "2025-06-14T20:56:24.335Z" },
]

[[package]]
name = "pyobjc-framework-webkit"
version = "11.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core" },
    { name = "pyobjc-framework-cocoa" },
]
sdist = { url = "https://files.pythonhosted.org/packages/92/04/fb3d0b68994f7e657ef00c1ac5fc1c04ae2fc7ea581d647f5ae1f6739b14/pyobjc_framework_webkit-11.1.tar.gz", hash = "sha256:27e701c7aaf4f24fc7e601a128e2ef14f2773f4ab071b9db7438dc5afb5053ae", size = 717102, upload-time = "2025-06-14T20:58:47.461Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/7e/fa2c18c0c0f9321e5036e54b9da7a196956b531e50fe1a76e7dfdbe8fac2/pyobjc_framework_webkit-11.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1a6e6f64ca53c4953f17e808ecac11da288d9a6ade738156ba161732a5e0c96a", size = 51464, upload-time = "2025-06-14T20:56:27.653Z" },
    { url = "https://files.pythonhosted.org/packages/7a/8d/66561d95b00b8e57a9d5725ae34a8d9ca7ebeb776f13add989421ff90279/pyobjc_framework_webkit-11.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:1d01008756c3912b02b7c02f62432467fbee90a93e3b8e31fa351b4ca97c9c98", size = 51495, upload-time = "2025-06-14T20:56:28.464Z" },
    { url = "https://files.pythonhosted.org/packages/db/c3/e790b518f84ea8dfbe32a9dcb4d8611b532de08057d19f853c1890110938/pyobjc_framework_webkit-11.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:864f9867a2caaeaeb83e5c0fa3dcf78169622233cf93a9a5eeb7012ced3b8076", size = 51985, upload-time = "2025-06-14T20:56:29.303Z" },
    { url = "https://files.pythonhosted.org/packages/d7/4f/194e3e7c01861a5e46dfe9e1fa28ad01fd07190cb514e41a7dcf1f0b7031/pyobjc_framework_webkit-11.1-cp314-cp314-macosx_11_0_universal2.whl", hash = "sha256:13b774d4244734cb77bf3c3648149c163f62acaa105243d7c48bb3fd856b5628", size = 52248, upload-time = "2025-06-14T20:56:30.158Z" },
    { url = "https://files.pythonhosted.org/packages/31/09/28884e7c10d3a76a76c2c8f55369dd96a90f0283800c68f5c764e1fb8e2e/pyobjc_framework_webkit-11.1-cp314-cp314t-macosx_11_0_universal2.whl", hash = "sha256:c1c00d549ab1d50e3d7e8f5f71352b999d2c32dc2365c299f317525eb9bff916", size = 52725, upload-time = "2025-06-14T20:56:30.993Z" },
]

[[package]]
name = "requests"
version = "2.32.5"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c9/74/b3ff8e6c8446842c3f5c837e9c3dfcfe2018ea6ecef224c710c85ef728f4/requests-2.32.5.tar.gz", hash = "sha256:dbba0bac56e100853db0ea71b82b4dfd5fe2bf6d3754a8893c3af500cec7d7cf", size = 134517, upload-time = "2025-08-18T20:46:02.573Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1e/db/4254e3eabe8020b458f1a747140d32277ec7a271daf1d235b70dc0b4e6e3/requests-2.32.5-py3-none-any.whl", hash = "sha256:2462f94637a34fd532264295e186976db0f5d453d1cdd31473c85a6a161affb6", size = 64738, upload-time = "2025-08-18T20:46:00.542Z" },
]

[[package]]
name = "soundfile"
version = "0.13.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "cffi" },
    { name = "numpy" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/41/9b873a8c055582859b239be17902a85339bec6a30ad162f98c9b0288a2cc/soundfile-0.13.1.tar.gz", hash = "sha256:b2c68dab1e30297317080a5b43df57e302584c49e2942defdde0acccc53f0e5b", size = 46156, upload-time = "2025-01-25T09:17:04.831Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/64/28/e2a36573ccbcf3d57c00626a21fe51989380636e821b341d36ccca0c1c3a/soundfile-0.13.1-py2.py3-none-any.whl", hash = "sha256:a23c717560da2cf4c7b5ae1142514e0fd82d6bbd9dfc93a50423447142f2c445", size = 25751, upload-time = "2025-01-25T09:16:44.235Z" },
    { url = "https://files.pythonhosted.org/packages/ea/ab/73e97a5b3cc46bba7ff8650a1504348fa1863a6f9d57d7001c6b67c5f20e/soundfile-0.13.1-py2.py3-none-macosx_10_9_x86_64.whl", hash = "sha256:82dc664d19831933fe59adad199bf3945ad06d84bc111a5b4c0d3089a5b9ec33", size = 1142250, upload-time = "2025-01-25T09:16:47.583Z" },
    { url = "https://files.pythonhosted.org/packages/a0/e5/58fd1a8d7b26fc113af244f966ee3aecf03cb9293cb935daaddc1e455e18/soundfile-0.13.1-py2.py3-none-macosx_11_0_arm64.whl", hash = "sha256:743f12c12c4054921e15736c6be09ac26b3b3d603aef6fd69f9dde68748f2593", size = 1101406, upload-time = "2025-01-25T09:16:49.662Z" },
    { url = "https://files.pythonhosted.org/packages/58/ae/c0e4a53d77cf6e9a04179535766b3321b0b9ced5f70522e4caf9329f0046/soundfile-0.13.1-py2.py3-none-manylinux_2_28_aarch64.whl", hash = "sha256:9c9e855f5a4d06ce4213f31918653ab7de0c5a8d8107cd2427e44b42df547deb", size = 1235729, upload-time = "2025-01-25T09:16:53.018Z" },
    { url = "https://files.pythonhosted.org/packages/57/5e/70bdd9579b35003a489fc850b5047beeda26328053ebadc1fb60f320f7db/soundfile-0.13.1-py2.py3-none-manylinux_2_28_x86_64.whl", hash = "sha256:03267c4e493315294834a0870f31dbb3b28a95561b80b134f0bd3cf2d5f0e618", size = 1313646, upload-time = "2025-01-25T09:16:54.872Z" },
    { url = "https://files.pythonhosted.org/packages/fe/df/8c11dc4dfceda14e3003bb81a0d0edcaaf0796dd7b4f826ea3e532146bba/soundfile-0.13.1-py2.py3-none-win32.whl", hash = "sha256:c734564fab7c5ddf8e9be5bf70bab68042cd17e9c214c06e365e20d64f9a69d5", size = 899881, upload-time = "2025-01-25T09:16:56.663Z" },
    { url = "https://files.pythonhosted.org/packages/14/e9/6b761de83277f2f02ded7e7ea6f07828ec78e4b229b80e4ca55dd205b9dc/soundfile-0.13.1-py2.py3-none-win_amd64.whl", hash = "sha256:1e70a05a0626524a69e9f0f4dd2ec174b4e9567f4d8b6c11d38b5c289be36ee9", size = 1019162, upload-time = "2025-01-25T09:16:59.573Z" },
]

[[package]]
name = "speechrecognition"
version = "3.14.3"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "audioop-lts", marker = "python_full_version >= '3.13'" },
    { name = "standard-aifc", marker = "python_full_version >= '3.13'" },
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a9/7b/51d8b756aa1066b3f95bcbe3795f382f630ca9d2559ed808dada022141bf/speechrecognition-3.14.3.tar.gz", hash = "sha256:bdd2000a9897832b33095e33adfa48580787255706092e1346d1c6c36adae0a4", size = 32858109, upload-time = "2025-05-12T23:42:29.671Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/aa/cd/4b5f5d04c8a4e25c376858d0ad28c325f079f17c82bf379185abf45e41bf/speechrecognition-3.14.3-py3-none-any.whl", hash = "sha256:1859fbb09ae23fa759200f5b0677307f1fb16e2c5c798f4259fcc41dd5399fe6", size = 32853520, upload-time = "2025-05-12T23:42:23.485Z" },
]

[[package]]
name = "standard-aifc"
version = "3.13.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "audioop-lts", marker = "python_full_version >= '3.13'" },
    { name = "standard-chunk", marker = "python_full_version >= '3.13'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c4/53/6050dc3dde1671eb3db592c13b55a8005e5040131f7509cef0215212cb84/standard_aifc-3.13.0.tar.gz", hash = "sha256:64e249c7cb4b3daf2fdba4e95721f811bde8bdfc43ad9f936589b7bb2fae2e43", size = 15240, upload-time = "2024-10-30T16:01:31.772Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c3/52/5fbb203394cc852334d1575cc020f6bcec768d2265355984dfd361968f36/standard_aifc-3.13.0-py3-none-any.whl", hash = "sha256:f7ae09cc57de1224a0dd8e3eb8f73830be7c3d0bc485de4c1f82b4a7f645ac66", size = 10492, upload-time = "2024-10-30T16:01:07.071Z" },
]

[[package]]
name = "standard-chunk"
version = "3.13.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/43/06/ce1bb165c1f111c7d23a1ad17204d67224baa69725bb6857a264db61beaf/standard_chunk-3.13.0.tar.gz", hash = "sha256:4ac345d37d7e686d2755e01836b8d98eda0d1a3ee90375e597ae43aaf064d654", size = 4672, upload-time = "2024-10-30T16:18:28.326Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7a/90/a5c1084d87767d787a6caba615aa50dc587229646308d9420c960cb5e4c0/standard_chunk-3.13.0-py3-none-any.whl", hash = "sha256:17880a26c285189c644bd5bd8f8ed2bdb795d216e3293e6dbe55bbd848e2982c", size = 4944, upload-time = "2024-10-30T16:18:26.694Z" },
]

[[package]]
name = "typing-extensions"
version = "4.15.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/72/94/1a15dd82efb362ac84269196e94cf00f187f7ed21c242792a923cdb1c61f/typing_extensions-4.15.0.tar.gz", hash = "sha256:0cea48d173cc12fa28ecabc3b837ea3cf6f38c6d1136f85cbaaf598984861466", size = 109391, upload-time = "2025-08-25T13:49:26.313Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl", hash = "sha256:f0fa19c6845758ab08074a0cfa8b7aecb71c999ca73d62883bc25cc018c4e548", size = 44614, upload-time = "2025-08-25T13:49:24.86Z" },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/15/22/9ee70a2574a4f4599c47dd506532914ce044817c7752a79b6a51286319bc/urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760", size = 393185, upload-time = "2025-06-18T14:07:41.644Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc", size = 129795, upload-time = "2025-06-18T14:07:40.39Z" },
]
