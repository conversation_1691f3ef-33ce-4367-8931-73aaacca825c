#!/usr/bin/env python3
"""
VoiceVox Configuration Tool
Tool for managing VoiceVox settings and optimizing performance
"""

import json
import os
from typing import Dict, Any
from voicevox_client import VoiceVoxClient

class VoiceVoxConfigTool:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load existing configuration or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self._get_default_config()
        else:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "voicevox": {
                "engine_url": "http://localhost:50021",
                "default_speaker_id": 1,
                "timeout_settings": {
                    "connection_timeout": 3,
                    "audio_query_timeout": 5,
                    "synthesis_timeout": 10
                },
                "performance": {
                    "enable_streaming": True,
                    "preload_speakers": True,
                    "cache_audio_queries": True,
                    "max_cache_size": 100,
                    "use_compression": True,
                    "parallel_processing": False
                },
                "audio": {
                    "sample_rate": 24000,
                    "bit_depth": 16,
                    "channels": 1,
                    "buffer_size": 1024,
                    "enable_audio_effects": False,
                    "volume": 1.0,
                    "speed_scale": 1.0,
                    "pitch_scale": 0.0,
                    "intonation_scale": 1.0
                },
                "speakers": {
                    "preferred_speakers": [1, 2, 3],
                    "fallback_speaker": 1,
                    "auto_detect_best": False
                },
                "optimization": {
                    "keep_alive_connections": True,
                    "connection_pool_size": 5,
                    "retry_attempts": 2,
                    "retry_delay": 0.1,
                    "enable_http2": False,
                    "compress_requests": True
                },
                "debug": {
                    "log_requests": False,
                    "log_audio_generation": False,
                    "measure_latency": True,
                    "verbose_errors": True
                }
            }
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✓ Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def optimize_for_speed(self):
        """Apply speed-optimized settings"""
        voicevox_config = self.config["voicevox"]
        
        # Reduce timeouts for faster response
        voicevox_config["timeout_settings"]["connection_timeout"] = 2
        voicevox_config["timeout_settings"]["audio_query_timeout"] = 3
        voicevox_config["timeout_settings"]["synthesis_timeout"] = 8
        
        # Enable all performance features
        voicevox_config["performance"]["cache_audio_queries"] = True
        voicevox_config["performance"]["max_cache_size"] = 200
        voicevox_config["performance"]["preload_speakers"] = True
        
        # Optimize audio settings
        voicevox_config["audio"]["buffer_size"] = 512  # Smaller buffer for lower latency
        voicevox_config["audio"]["speed_scale"] = 1.1  # Slightly faster speech
        
        # Optimize network settings
        voicevox_config["optimization"]["keep_alive_connections"] = True
        voicevox_config["optimization"]["connection_pool_size"] = 10
        voicevox_config["optimization"]["retry_attempts"] = 1  # Fewer retries for speed
        
        print("✓ Applied speed optimization settings")
    
    def optimize_for_quality(self):
        """Apply quality-optimized settings"""
        voicevox_config = self.config["voicevox"]
        
        # Longer timeouts for better quality
        voicevox_config["timeout_settings"]["synthesis_timeout"] = 20
        
        # Quality audio settings
        voicevox_config["audio"]["sample_rate"] = 48000  # Higher quality
        voicevox_config["audio"]["speed_scale"] = 1.0
        voicevox_config["audio"]["enable_audio_effects"] = True
        
        # More retries for reliability
        voicevox_config["optimization"]["retry_attempts"] = 3
        
        print("✓ Applied quality optimization settings")
    
    def set_speaker(self, speaker_id: int):
        """Set default speaker"""
        self.config["voicevox"]["default_speaker_id"] = speaker_id
        print(f"✓ Default speaker set to ID: {speaker_id}")
    
    def enable_debug_mode(self, enable: bool = True):
        """Enable or disable debug mode"""
        debug_config = self.config["voicevox"]["debug"]
        debug_config["log_requests"] = enable
        debug_config["log_audio_generation"] = enable
        debug_config["measure_latency"] = enable
        debug_config["verbose_errors"] = enable
        
        status = "enabled" if enable else "disabled"
        print(f"✓ Debug mode {status}")
    
    def test_performance(self):
        """Test VoiceVox performance with current settings"""
        print("\n🧪 Testing VoiceVox Performance...")
        print("-" * 40)
        
        try:
            client = VoiceVoxClient(config_file=self.config_file)
            
            if not client.check_voicevox_status():
                print("❌ VoiceVox engine is not running")
                return
            
            # Test with different text lengths
            test_texts = [
                "こんにちは！",
                "今日はいい天気ですね！",
                "AIバーチャルユーチューバーとして、皆さんとお話しできて嬉しいです！"
            ]
            
            for i, text in enumerate(test_texts, 1):
                print(f"\nTest {i}: {text}")
                start_time = time.time()
                success = client.text_to_speech(text)
                end_time = time.time()
                
                if success:
                    latency = end_time - start_time
                    print(f"✅ Success - Latency: {latency:.2f}s")
                else:
                    print("❌ Failed")
            
            # Show cache stats
            stats = client.get_cache_stats()
            print(f"\n📊 Cache Stats: {stats['cache_size']} queries cached")
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")

def main():
    """Interactive configuration tool"""
    tool = VoiceVoxConfigTool()
    
    print("🔧 VoiceVox Configuration Tool")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Optimize for speed")
        print("2. Optimize for quality") 
        print("3. Set default speaker")
        print("4. Enable/disable debug mode")
        print("5. Test performance")
        print("6. Save and exit")
        print("7. Exit without saving")
        
        choice = input("\nEnter your choice (1-7): ").strip()
        
        if choice == "1":
            tool.optimize_for_speed()
        elif choice == "2":
            tool.optimize_for_quality()
        elif choice == "3":
            try:
                speaker_id = int(input("Enter speaker ID: "))
                tool.set_speaker(speaker_id)
            except ValueError:
                print("Invalid speaker ID")
        elif choice == "4":
            enable = input("Enable debug mode? (y/n): ").lower() == 'y'
            tool.enable_debug_mode(enable)
        elif choice == "5":
            tool.test_performance()
        elif choice == "6":
            tool.save_config()
            print("👋 Configuration saved. Goodbye!")
            break
        elif choice == "7":
            print("👋 Exiting without saving. Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
