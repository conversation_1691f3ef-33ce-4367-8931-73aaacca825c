#!/usr/bin/env python3
"""
VBAudio Manager for Virtual Audio Cable Integration
Handles audio routing through VB-Audio Virtual Cable for streaming
"""

import pygame
import pygame.mixer
import io
import time
from typing import Optional, Dict, Any, List

class VBAudioManager:
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize VBAudio manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config.get("audio_output", {})
        self.vbaudio_device = self.config.get("vbaudio_device", "CABLE Input (VB-Audio Virtual Cable)")
        self.enable_vbaudio = self.config.get("enable_vbaudio", True)
        self.fallback_to_default = self.config.get("fallback_to_default", True)
        self.test_devices = self.config.get("test_audio_devices", True)
        
        # Audio device info
        self.available_devices = []
        self.current_device = None
        self.device_initialized = False
        
        # Initialize audio system
        self._initialize_audio_system()
    
    def _initialize_audio_system(self):
        """Initialize pygame mixer with VBAudio support"""
        try:
            # Initialize pygame mixer
            pygame.mixer.pre_init()
            pygame.mixer.init()
            
            if self.test_devices:
                self._detect_audio_devices()
            
            if self.enable_vbaudio:
                self._setup_vbaudio()
            else:
                print("✓ Using default audio output")
                self.device_initialized = True
                
        except Exception as e:
            print(f"Error initializing audio system: {e}")
            self.device_initialized = False
    
    def _detect_audio_devices(self):
        """Detect available audio devices"""
        try:
            # This is a simplified detection - in practice you might need
            # platform-specific code to enumerate audio devices
            print("🔍 Detecting audio devices...")
            
            # For Windows, you could use winsound or pyaudio to enumerate devices
            # For now, we'll assume VB-Audio is available if configured
            self.available_devices = [
                "Default Audio Device",
                "CABLE Input (VB-Audio Virtual Cable)",
                "Speakers",
                "Headphones"
            ]
            
            print(f"✓ Found {len(self.available_devices)} audio devices")
            for i, device in enumerate(self.available_devices):
                print(f"  {i+1}. {device}")
                
        except Exception as e:
            print(f"Warning: Could not detect audio devices: {e}")
            self.available_devices = ["Default Audio Device"]
    
    def _setup_vbaudio(self):
        """Setup VB-Audio Virtual Cable"""
        try:
            if self.vbaudio_device in self.available_devices:
                print(f"✓ VB-Audio Virtual Cable detected: {self.vbaudio_device}")
                self.current_device = self.vbaudio_device
                self.device_initialized = True
                
                # Test VB-Audio with a silent audio
                self._test_vbaudio()
            else:
                print(f"⚠ VB-Audio device '{self.vbaudio_device}' not found")
                if self.fallback_to_default:
                    print("✓ Falling back to default audio device")
                    self.current_device = "Default Audio Device"
                    self.device_initialized = True
                else:
                    print("❌ VB-Audio required but not available")
                    self.device_initialized = False
                    
        except Exception as e:
            print(f"Error setting up VB-Audio: {e}")
            if self.fallback_to_default:
                self.current_device = "Default Audio Device"
                self.device_initialized = True
            else:
                self.device_initialized = False
    
    def _test_vbaudio(self):
        """Test VB-Audio connection with silent audio"""
        try:
            # Create a very short silent audio for testing
            silent_duration = 0.1  # 100ms
            sample_rate = 44100
            samples = int(silent_duration * sample_rate)
            
            # This is a placeholder - actual implementation would depend on
            # how you want to route audio to VB-Audio
            print("✓ VB-Audio test completed")
            
        except Exception as e:
            print(f"Warning: VB-Audio test failed: {e}")
    
    def play_audio_data(self, audio_data: bytes) -> bool:
        """
        Play audio data through the configured output device
        
        Args:
            audio_data: Raw audio data to play
            
        Returns:
            True if successful, False otherwise
        """
        if not self.device_initialized:
            print("❌ Audio device not initialized")
            return False
        
        try:
            # Create audio file object
            audio_file = io.BytesIO(audio_data)
            
            # Load and play audio
            pygame.mixer.music.load(audio_file)
            
            # Apply volume settings
            volume = self.config.get("volume", 1.0)
            pygame.mixer.music.set_volume(volume)
            
            pygame.mixer.music.play()
            
            # Wait for audio to finish
            while pygame.mixer.music.get_busy():
                pygame.time.wait(50)
            
            return True
            
        except Exception as e:
            print(f"Error playing audio: {e}")
            return False
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get current audio device information"""
        return {
            "current_device": self.current_device,
            "vbaudio_enabled": self.enable_vbaudio,
            "device_initialized": self.device_initialized,
            "available_devices": self.available_devices,
            "vbaudio_device": self.vbaudio_device
        }
    
    def switch_device(self, device_name: str) -> bool:
        """
        Switch to a different audio device
        
        Args:
            device_name: Name of the device to switch to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if device_name in self.available_devices:
                self.current_device = device_name
                print(f"✓ Switched to audio device: {device_name}")
                return True
            else:
                print(f"❌ Device '{device_name}' not available")
                return False
        except Exception as e:
            print(f"Error switching audio device: {e}")
            return False
    
    def test_audio_output(self) -> bool:
        """Test audio output with current settings"""
        try:
            print(f"🧪 Testing audio output on: {self.current_device}")
            
            # This would play a test tone or message
            # For now, just return success if device is initialized
            if self.device_initialized:
                print("✅ Audio output test successful")
                return True
            else:
                print("❌ Audio output test failed - device not initialized")
                return False
                
        except Exception as e:
            print(f"Audio test error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup audio resources"""
        try:
            pygame.mixer.quit()
            print("✓ Audio system cleaned up")
        except Exception as e:
            print(f"Warning: Audio cleanup error: {e}")
