#!/usr/bin/env python3
"""
Demo script to showcase different input modes for the AI Vtuber
"""

import json
import time

def show_input_modes():
    """Display information about available input modes"""
    print("🎮 AI Vtuber Input Modes Demo")
    print("=" * 50)
    
    print("\n📋 Available Input Modes:")
    print("-" * 30)
    
    print("1. 🎤 AUTO MODE")
    print("   • Continuous listening with timeout")
    print("   • Automatically starts recording when you speak")
    print("   • Stops after silence or timeout")
    print("   • Good for: Hands-free operation")
    
    print("\n2. 🎮 BUTTON MODE")
    print("   • Manual recording control")
    print("   • Press and hold configured button to record")
    print("   • Release button to stop recording")
    print("   • Good for: Precise control, noisy environments")
    
    print("\n3. 🎤🎮 HYBRID MODE (BOTH)")
    print("   • Combines both auto and button modes")
    print("   • Auto listening OR button recording")
    print("   • Best of both worlds")
    print("   • Good for: Maximum flexibility")
    
    print("\n⚙️ Configuration Options:")
    print("-" * 30)
    print("• record_button: Which key to use (default: 'space')")
    print("• button_hold_mode: true = hold to record, false = toggle")
    print("• mode: 'auto', 'button', or 'both'")
    
    print("\n🔧 How to Configure:")
    print("-" * 30)
    print("Edit config.json -> input_control section:")
    print("""
{
  "input_control": {
    "mode": "both",           // "auto", "button", or "both"
    "record_button": "space", // Key to use for recording
    "button_hold_mode": true, // Hold vs toggle mode
    "show_recording_status": true
  }
}
""")

def show_current_config():
    """Show current input configuration"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        input_config = config.get("input_control", {})
        
        print("\n📄 Current Configuration:")
        print("-" * 30)
        print(f"Mode: {input_config.get('mode', 'auto')}")
        print(f"Record button: {input_config.get('record_button', 'space')}")
        print(f"Hold mode: {input_config.get('button_hold_mode', True)}")
        print(f"Show status: {input_config.get('show_recording_status', True)}")
        
    except Exception as e:
        print(f"Error reading config: {e}")

def show_usage_examples():
    """Show usage examples for each mode"""
    print("\n💡 Usage Examples:")
    print("-" * 30)
    
    print("\n🎤 AUTO MODE Usage:")
    print("1. Run: uv run python main.py")
    print("2. Wait for 'Listening...' message")
    print("3. Just speak naturally")
    print("4. AI responds automatically")
    
    print("\n🎮 BUTTON MODE Usage:")
    print("1. Set mode to 'button' in config.json")
    print("2. Run: uv run python main.py")
    print("3. Press and HOLD spacebar (or configured key)")
    print("4. Speak while holding the button")
    print("5. Release button when done")
    print("6. AI processes and responds")
    
    print("\n🎤🎮 HYBRID MODE Usage:")
    print("1. Set mode to 'both' in config.json")
    print("2. Run: uv run python main.py")
    print("3. Either:")
    print("   • Speak naturally (auto mode)")
    print("   • OR hold spacebar and speak (button mode)")
    print("4. AI responds with Japanese audio + bilingual text")

def show_voice_commands():
    """Show available voice commands"""
    print("\n🗣️ Available Voice Commands:")
    print("-" * 30)
    print("• 'input stats' - Show input system status")
    print("• 'translation stats' - Show translation cache info")
    print("• 'voice stats' - Show voice synthesis stats")
    print("• 'clear translation cache' - Clear translation cache")
    print("• 'clear voice cache' - Clear voice synthesis cache")
    print("• 'goodbye' or 'exit' - Stop the application")

def main():
    """Main demo function"""
    show_input_modes()
    show_current_config()
    show_usage_examples()
    show_voice_commands()
    
    print("\n🚀 Ready to Try?")
    print("-" * 30)
    print("Run: uv run python main.py")
    print("\nThe AI will:")
    print("✓ Translate responses to Japanese")
    print("✓ Display both Japanese and English")
    print("✓ Use Japanese for voice output")
    print("✓ Support flexible input modes")
    
    print("\n📝 Quick Test:")
    print("1. Say: 'Hello, how are you?'")
    print("2. Expect output like:")
    print("   Jp: こんにちは、元気ですか？")
    print("   En: Hello, how are you?")
    print("3. Voice will speak the Japanese version")

if __name__ == "__main__":
    main()
