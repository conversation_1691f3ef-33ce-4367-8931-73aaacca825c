import threading
import time
from typing import Optional, Dict, Any, Callable
import speech_recognition as sr

try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False
    print("⚠ keyboard module not available. Install with: pip install keyboard")

class InputManager:
    """Manages input methods for speech recording - both button-based and automatic"""
    
    def __init__(self, config: Dict[str, Any], recognizer: sr.Recognizer, microphone: sr.Microphone):
        self.config = config
        self.input_config = config.get("input_control", {})
        self.recognizer = recognizer
        self.microphone = microphone
        
        # Input mode configuration
        self.input_mode = self.input_config.get("mode", "auto")  # "auto", "button", "both"
        self.record_button = self.input_config.get("record_button", "space")
        self.button_hold_mode = self.input_config.get("button_hold_mode", True)  # True = hold to record, False = toggle
        
        # State management
        self.is_recording = False
        self.is_button_pressed = False
        self.recording_thread = None
        self.button_listener_active = False
        
        # Callbacks
        self.on_speech_detected: Optional[Callable[[str], None]] = None
        self.on_recording_start: Optional[Callable[[], None]] = None
        self.on_recording_stop: Optional[Callable[[], None]] = None
        
        # Speech recognition settings
        speech_config = config.get("speech_recognition", {})
        self.language = speech_config.get("language", "id-ID")
        self.timeout = speech_config.get("timeout", 5)
        self.phrase_limit = speech_config.get("phrase_time_limit", 10)
        
        print(f"✓ Input Manager initialized - Mode: {self.input_mode}")
        if self.input_mode in ["button", "both"] and KEYBOARD_AVAILABLE:
            print(f"  Record button: {self.record_button}")
            print(f"  Hold mode: {'Hold to record' if self.button_hold_mode else 'Toggle recording'}")
        elif self.input_mode in ["button", "both"] and not KEYBOARD_AVAILABLE:
            print("  ⚠ Button mode requested but keyboard module not available")
            self.input_mode = "auto"  # Fallback to auto mode
    
    def set_callbacks(self, 
                     on_speech_detected: Optional[Callable[[str], None]] = None,
                     on_recording_start: Optional[Callable[[], None]] = None,
                     on_recording_stop: Optional[Callable[[], None]] = None):
        """Set callback functions for speech events"""
        self.on_speech_detected = on_speech_detected
        self.on_recording_start = on_recording_start
        self.on_recording_stop = on_recording_stop
    
    def start_input_monitoring(self):
        """Start monitoring for input based on the configured mode"""
        if self.input_mode == "auto":
            print("🎤 Auto recording mode - Listening continuously...")
        elif self.input_mode == "button" and KEYBOARD_AVAILABLE:
            print(f"🎮 Button recording mode - Press and hold '{self.record_button}' to record")
            self._start_button_listener()
        elif self.input_mode == "both" and KEYBOARD_AVAILABLE:
            print(f"🎤🎮 Hybrid mode - Auto listening OR press '{self.record_button}' to record")
            self._start_button_listener()
        else:
            print("🎤 Fallback to auto recording mode")
            self.input_mode = "auto"
    
    def stop_input_monitoring(self):
        """Stop all input monitoring"""
        self.button_listener_active = False
        if self.is_recording:
            self._stop_recording()
    
    def _start_button_listener(self):
        """Start listening for button presses"""
        if not KEYBOARD_AVAILABLE:
            return
        
        self.button_listener_active = True
        
        def button_listener():
            while self.button_listener_active:
                try:
                    if keyboard.is_pressed(self.record_button):
                        if not self.is_button_pressed:
                            self.is_button_pressed = True
                            if self.button_hold_mode:
                                # Hold mode: start recording when button pressed
                                self._start_button_recording()
                            else:
                                # Toggle mode: toggle recording state
                                if self.is_recording:
                                    self._stop_recording()
                                else:
                                    self._start_button_recording()
                    else:
                        if self.is_button_pressed:
                            self.is_button_pressed = False
                            if self.button_hold_mode and self.is_recording:
                                # Hold mode: stop recording when button released
                                self._stop_recording()
                    
                    time.sleep(0.05)  # Check every 50ms
                except Exception as e:
                    print(f"Button listener error: {e}")
                    break
        
        button_thread = threading.Thread(target=button_listener, daemon=True)
        button_thread.start()
    
    def _start_button_recording(self):
        """Start recording triggered by button press"""
        if self.is_recording:
            return
        
        self.is_recording = True
        print(f"🔴 Recording started (button: {self.record_button})")
        
        if self.on_recording_start:
            self.on_recording_start()
        
        # Start recording in a separate thread
        self.recording_thread = threading.Thread(target=self._record_speech, daemon=True)
        self.recording_thread.start()
    
    def _stop_recording(self):
        """Stop current recording"""
        if not self.is_recording:
            return
        
        self.is_recording = False
        print("⏹️ Recording stopped")
        
        if self.on_recording_stop:
            self.on_recording_stop()
    
    def _record_speech(self):
        """Record speech and process it"""
        try:
            with self.microphone as source:
                if self.input_mode == "button" or (self.input_mode == "both" and self.is_button_pressed):
                    # Button-triggered recording - record until button released or timeout
                    print("🎤 Listening for speech...")
                    if self.button_hold_mode:
                        # For hold mode, record while button is pressed
                        audio = self._record_while_button_pressed()
                    else:
                        # For toggle mode, record with timeout
                        audio = self.recognizer.listen(source, timeout=self.timeout, phrase_time_limit=self.phrase_limit)
                else:
                    # Auto mode recording
                    audio = self.recognizer.listen(source, timeout=self.timeout, phrase_time_limit=self.phrase_limit)
            
            if audio:
                print("🔄 Processing speech...")
                text = self.recognizer.recognize_google(audio, language=self.language)
                print(f"📝 Recognized: {text}")
                
                if self.on_speech_detected:
                    self.on_speech_detected(text)
                
                return text
        
        except sr.WaitTimeoutError:
            print("⏰ No speech detected within timeout period")
        except sr.UnknownValueError:
            print("❓ Could not understand the audio")
        except sr.RequestError as e:
            print(f"❌ Error with speech recognition service: {e}")
        except Exception as e:
            print(f"❌ Recording error: {e}")
        
        finally:
            self.is_recording = False
        
        return None
    
    def _record_while_button_pressed(self):
        """Record audio while button is being held down"""
        if not KEYBOARD_AVAILABLE:
            return None
        
        frames = []
        
        with self.microphone as source:
            # Start recording
            self.recognizer.adjust_for_ambient_noise(source, duration=0.1)
            
            start_time = time.time()
            max_duration = self.phrase_limit
            
            while (self.is_button_pressed and 
                   keyboard.is_pressed(self.record_button) and 
                   (time.time() - start_time) < max_duration):
                
                try:
                    # Record small chunks while button is pressed
                    audio_chunk = self.recognizer.listen(source, timeout=0.1, phrase_time_limit=0.5)
                    frames.append(audio_chunk)
                except sr.WaitTimeoutError:
                    continue
                except Exception as e:
                    print(f"Recording chunk error: {e}")
                    break
        
        # Combine all audio chunks
        if frames:
            # For simplicity, return the last chunk (in a real implementation, you'd combine them)
            return frames[-1] if frames else None
        
        return None
    
    def listen_for_speech_auto(self) -> Optional[str]:
        """Auto mode: Listen for speech with timeout (original behavior)"""
        try:
            if self.on_recording_start:
                self.on_recording_start()
            
            with self.microphone as source:
                print("🎤 Listening... Speak now!")
                audio = self.recognizer.listen(source, timeout=self.timeout, phrase_time_limit=self.phrase_limit)
            
            print("🔄 Processing speech...")
            text = self.recognizer.recognize_google(audio, language=self.language)
            print(f"📝 You said: {text}")
            
            if self.on_recording_stop:
                self.on_recording_stop()
            
            return text
        
        except sr.WaitTimeoutError:
            print("⏰ No speech detected within timeout period")
        except sr.UnknownValueError:
            print("❓ Could not understand the audio")
        except sr.RequestError as e:
            print(f"❌ Error with speech recognition service: {e}")
        
        if self.on_recording_stop:
            self.on_recording_stop()
        
        return None
    
    def get_input_status(self) -> Dict[str, Any]:
        """Get current input status"""
        return {
            "mode": self.input_mode,
            "is_recording": self.is_recording,
            "is_button_pressed": self.is_button_pressed,
            "record_button": self.record_button,
            "button_hold_mode": self.button_hold_mode,
            "keyboard_available": KEYBOARD_AVAILABLE
        }
