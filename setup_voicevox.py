#!/usr/bin/env python3
"""
Setup script for VOICEVOX integration
"""

import requests
import webbrowser
import time
from voicevox_client import VoiceVoxClient

def download_voicevox_info():
    """Provide information about downloading VOICEVOX"""
    print("🎤 VOICEVOX Setup Instructions")
    print("=" * 50)
    print()
    print("To enable voice output, you need to install VOICEVOX Engine:")
    print()
    print("1. Download VOICEVOX Engine from:")
    print("   https://github.com/VOICEVOX/voicevox/releases")
    print()
    print("2. Look for the latest release and download:")
    print("   - Windows: voicevox-windows-*.zip")
    print("   - Extract and run voicevox.exe")
    print()
    print("3. The engine will start on http://localhost:50021")
    print()
    
    choice = input("Would you like me to open the download page? (y/n): ").lower()
    if choice == 'y':
        webbrowser.open("https://github.com/VOICEVOX/voicevox/releases")
        print("✓ Opened VOICEVOX releases page in your browser")

def test_voicevox_connection():
    """Test connection to VOICEVOX engine"""
    print("\n🔍 Testing VOICEVOX Connection...")
    print("-" * 30)
    
    client = VoiceVoxClient()
    
    if client.check_voicevox_status():
        print("✅ VOICEVOX Engine is running!")
        
        # Get and display speakers
        speakers = client.get_speakers()
        if speakers:
            print(f"\n📢 Found {len(speakers)} available speakers:")
            for i, speaker in enumerate(speakers[:10]):  # Show first 10
                styles = speaker.get('styles', [])
                style_names = [style.get('name', 'Unknown') for style in styles]
                print(f"  {i+1}. {speaker.get('name', 'Unknown')} - Styles: {', '.join(style_names)}")
        
        # Test voice synthesis
        print("\n🎵 Testing voice synthesis...")
        test_text = "こんにちは！私はAIバーチャルユーチューバーです！"
        success = client.text_to_speech(test_text)
        
        if success:
            print("✅ Voice synthesis test successful!")
        else:
            print("❌ Voice synthesis test failed")
            
        return True
    else:
        print("❌ VOICEVOX Engine is not running")
        print("\nPlease make sure:")
        print("1. VOICEVOX is installed")
        print("2. VOICEVOX Engine is running")
        print("3. Engine is accessible at http://localhost:50021")
        return False

def main():
    print("🤖 AI Vtuber - VOICEVOX Setup")
    print("=" * 50)
    
    # Test if VOICEVOX is already running
    if not test_voicevox_connection():
        print()
        download_voicevox_info()
        
        print("\nAfter installing and starting VOICEVOX Engine, run this script again to test.")
    else:
        print("\n🎉 VOICEVOX is ready! You can now run the AI Vtuber with voice output.")
        print("Run: uv run python main.py")

if __name__ == "__main__":
    main()
