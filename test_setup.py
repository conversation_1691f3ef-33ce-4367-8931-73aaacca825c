#!/usr/bin/env python3
"""
Test script to verify the AI Vtuber setup
"""

def test_imports():
    """Test if all required packages can be imported"""
    try:
        import speech_recognition as sr
        print("✓ SpeechRecognition imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import SpeechRecognition: {e}")
        return False
    
    try:
        import requests
        print("✓ Requests imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import Requests: {e}")
        return False
    
    try:
        import pyaudio
        print("✓ PyAudio imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import PyAudio: {e}")
        return False
    
    return True

def test_microphone():
    """Test if microphone is accessible"""
    try:
        import speech_recognition as sr
        r = sr.Recognizer()
        mic = sr.Microphone()
        print("✓ Microphone initialized successfully")
        
        # List available microphones
        print("\nAvailable microphones:")
        for index, name in enumerate(sr.Microphone.list_microphone_names()):
            print(f"  {index}: {name}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to initialize microphone: {e}")
        return False

def test_pollinations_api():
    """Test if Pollinations API is accessible"""
    try:
        import requests
        url = "https://text.pollinations.ai/"
        test_prompt = "Hello, this is a test message."
        
        response = requests.post(url, 
                               json={"messages": [{"role": "user", "content": test_prompt}]},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            print("✓ Pollinations API is accessible")
            print(f"  Test response: {response.text[:100]}...")
            return True
        else:
            print(f"✗ Pollinations API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to connect to Pollinations API: {e}")
        return False

if __name__ == "__main__":
    print("Testing AI Vtuber Setup")
    print("=" * 40)
    
    all_tests_passed = True
    
    print("\n1. Testing imports...")
    all_tests_passed &= test_imports()
    
    print("\n2. Testing microphone...")
    all_tests_passed &= test_microphone()
    
    print("\n3. Testing Pollinations API...")
    all_tests_passed &= test_pollinations_api()
    
    print("\n" + "=" * 40)
    if all_tests_passed:
        print("✓ All tests passed! Ready to run the AI Vtuber.")
    else:
        print("✗ Some tests failed. Please check the errors above.")
